# Design Document: PostViewerFullScreen Redesign

## Overview

This design document outlines the approach for redesigning the PostViewerFullScreen component in the Notely Chrome Extension. The goal is to create a visually appealing, cohesive, and functional interface that works well in both dark and light modes, with particular focus on improving the tags and categories section. The redesign will leverage existing UI components and animation utilities while introducing new design patterns to enhance the user experience.

## Architecture

The redesigned PostViewerFullScreen component will follow these architectural principles:

1. **Component-Based Structure**: The component will be broken down into smaller, reusable sub-components for better maintainability.
2. **Theme-Aware Design**: All elements will be designed to adapt to both light and dark themes using the application's theming system.
3. **Responsive Layout**: The component will use a responsive layout that works well across different screen sizes.
4. **Animation Integration**: The component will incorporate the animation utilities from the UI animation enhancements feature.

## Components and Interfaces

### PostViewerFullScreen Component Structure

The PostViewerFullScreen component will be restructured into the following sub-components:

1. **PostViewerHeader**: Contains the post header information, including platform logo, author, timestamp, and close button.
2. **PostViewerContent**: Displays the main post content, including text and media.
3. **PostViewerSidebar**: Contains metadata, tags, categories, and notes sections.
4. **TagsPanel**: A redesigned component for displaying and managing tags.
5. **CategoriesPanel**: A redesigned component for displaying and managing categories.
6. **NotesEditor**: A component for adding and editing notes.

### Component Interfaces

```typescript
// PostViewerFullScreen Component Props
interface PostViewerFullScreenProps {
  post: PostWithAIData | null;
  onClose: () => void;
  onAddCategory: (postId: string, category: string) => void;
  onAddTag?: (postId: string, tag: string) => void;
  onRemoveTag?: (postId: string, tag: string) => void;
  onRemoveCategory?: (postId: string, category: string) => void;
  onUpdateNotes?: (postId: string, notes: string) => void;
  onUpdateCategories?: (postId: string, categories: string[]) => void;
  onUpdateTags?: (postId: string, tags: string[]) => void;
  threadPosts?: PostWithAIData[];
  currentThreadIndex?: number;
  onThreadNavigate?: (index: number) => void;
}

// TagsPanel Component Props
interface TagsPanelProps {
  tags: string[];
  onAddTag: (tag: string) => void;
  onRemoveTag: (tag: string) => void;
  onUpdateTags: (tags: string[]) => void;
  allTags: string[]; // For autocomplete
  maxTags?: number;
}

// CategoriesPanel Component Props
interface CategoriesPanelProps {
  categories: string[];
  onAddCategory: (category: string) => void;
  onRemoveCategory: (category: string) => void;
  onUpdateCategories: (categories: string[]) => void;
  allCategories: string[]; // For autocomplete
  maxCategories?: number;
}
```

## Design Details

### Layout Structure

The redesigned PostViewerFullScreen will use a two-column layout on larger screens and a single-column layout on smaller screens:

```
┌─────────────────────────────────────────────────────────────────┐
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                      PostViewerHeader                       │ │
│ └─────────────────────────────────────────────────────────────┘ │
│ ┌───────────────────────────────┐ ┌───────────────────────────┐ │
│ │                               │ │                           │ │
│ │                               │ │       PostViewerSidebar   │ │
│ │                               │ │                           │ │
│ │      PostViewerContent        │ │  - Insight Section        │ │
│ │                               │ │  - TagsPanel              │ │
│ │                               │ │  - CategoriesPanel        │ │
│ │                               │ │  - NotesEditor            │ │
│ │                               │ │                           │ │
│ └───────────────────────────────┘ └───────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

On smaller screens, the layout will stack vertically:

```
┌─────────────────────────────────────────────┐
│ ┌─────────────────────────────────────────┐ │
│ │            PostViewerHeader             │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │                                         │ │
│ │            PostViewerContent            │ │
│ │                                         │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │                                         │ │
│ │            PostViewerSidebar            │ │
│ │                                         │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
```

### Color Scheme and Theming

The component will use the application's theme variables to ensure consistency with the rest of the application:

```css
/* Light Theme Colors */
--notely-bg: #ffffff;
--notely-card: #f8f9fa;
--notely-surface: #f0f2f5;
--notely-border: #e4e6eb;
--notely-text-primary: #1c1e21;
--notely-text-secondary: #65676b;
--notely-accent: #0066ff;

/* Dark Theme Colors */
--notely-bg-dark: #18191a;
--notely-card-dark: #242526;
--notely-surface-dark: #3a3b3c;
--notely-border-dark: #3e4042;
--notely-text-primary-dark: #e4e6eb;
--notely-text-secondary-dark: #b0b3b8;
--notely-accent-dark: #2d88ff;
```

### Tags and Categories Redesign

The tags and categories sections will be redesigned to be more visually appealing and functional:

#### Tags Panel

The tags panel will use a modern chip design with the following features:

- Tags will be displayed as interactive chips with subtle background colors
- Each tag will have a remove button that appears on hover
- Tags will have a subtle hover effect for better interactivity
- The add tag input will be integrated seamlessly with the tag chips
- Autocomplete suggestions will appear when typing in the add tag input
- The panel will handle overflow with horizontal scrolling on mobile and wrapping on desktop

#### Categories Panel

The categories panel will use a similar design to the tags panel but with visual distinctions:

- Categories will have a slightly different visual style than tags (e.g., rounded rectangles instead of pills)
- Categories will be grouped if there are many of them
- Each category will have a remove button that appears on hover
- The add category input will support autocomplete
- The panel will handle overflow with vertical scrolling

### Animation and Interaction Design

The component will incorporate animations for a more engaging user experience:

- The component will fade in and scale slightly when opened
- Tags and categories will animate when added or removed
- Feedback messages will fade in and out
- Buttons and interactive elements will have hover and active state animations
- The notes section will expand smoothly when focused

## Data Models

The component will continue to use the existing PostWithAIData interface, with no changes required to the data model:

```typescript
export interface PostWithAIData {
  id: string;
  platform: Platform;
  mediaType: 'image' | 'video' | 'text';
  mediaUrl?: string;
  text?: string;
  content?: string;
  author: string;
  timestamp: string;
  stats?: { likes?: number; comments?: number; shares?: number };
  snapNote?: string | null;
  notes?: string | null;
  inSight?: AIInSightData | null;
  fastTake?: string | null;
  tags?: string[] | null;
  categories?: string[] | null;
  media?: MediaItem[];
  isThread?: boolean;
  threadId?: string;
  threadPosition?: number;
  threadLength?: number;
}
```

## Error Handling

The component will implement the following error handling strategies:

1. **Graceful Degradation**: If any part of the component fails to load or render, the rest of the component should still function correctly.
2. **Feedback Messages**: Clear error messages will be displayed when operations fail (e.g., saving notes, adding tags).
3. **Fallback UI**: If media content fails to load, appropriate fallback UI will be displayed.
4. **Input Validation**: User inputs will be validated to prevent errors.

## Testing Strategy

The redesigned component will be tested using the following approaches:

1. **Visual Testing**: Manual testing to ensure the component looks correct in both light and dark modes.
2. **Functional Testing**: Testing all interactive elements and operations.
3. **Responsive Testing**: Testing the component across different screen sizes.
4. **Accessibility Testing**: Testing with screen readers and keyboard navigation.
5. **Performance Testing**: Ensuring the component performs well with large amounts of content.

## Implementation Details

### PostViewerHeader Component

```jsx
const PostViewerHeader = ({ post, onClose }) => (
  <div className="flex items-center justify-between p-4 border-b border-notely-border dark:border-notely-border-dark">
    <div className="flex items-center">
      <PlatformLogo platform={post.platform} className="mr-3" />
      <div>
        <h2 className="font-medium text-notely-text-primary dark:text-notely-text-primary-dark">
          {post.author}
        </h2>
        <p className="text-sm text-notely-text-secondary dark:text-notely-text-secondary-dark">
          {formatTimestamp(post.timestamp)}
        </p>
      </div>
    </div>
    <button
      onClick={onClose}
      className="p-2 rounded-full hover:bg-notely-surface dark:hover:bg-notely-surface-dark transition-colors"
      aria-label="Close"
    >
      <IconX className="w-5 h-5 text-notely-text-secondary dark:text-notely-text-secondary-dark" />
    </button>
  </div>
);
```

### TagsPanel Component

```jsx
const TagsPanel = ({ tags, onAddTag, onRemoveTag, onUpdateTags, allTags, maxTags }) => {
  const [inputValue, setInputValue] = useState('');
  const [message, setMessage] = useState(null);
  
  const handleAddTag = (tag) => {
    if (!tag) return;
    
    const formattedTag = formatForStorage(tag);
    if (tags.includes(formattedTag)) {
      setMessage({ type: 'error', text: 'Tag already exists' });
      setTimeout(() => setMessage(null), 3000);
      return;
    }
    
    if (maxTags && tags.length >= maxTags) {
      setMessage({ type: 'error', text: `Maximum ${maxTags} tags allowed` });
      setTimeout(() => setMessage(null), 3000);
      return;
    }
    
    onAddTag(formattedTag);
    setInputValue('');
  };
  
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-notely-text-primary dark:text-notely-text-primary-dark">
          Tags
        </h3>
        {message && (
          <InlineFeedback
            type={message.type}
            message={message.text}
            isVisible={!!message}
            className="text-xs py-1"
          />
        )}
      </div>
      
      <div className="flex flex-wrap gap-2 mb-2">
        <AnimatedList
          items={tags || []}
          keyExtractor={(tag) => tag}
          renderItem={(tag) => (
            <Chip
              text={formatForDisplay(tag)}
              onRemove={() => onRemoveTag(tag)}
              className="bg-notely-accent/10 text-notely-accent dark:bg-notely-accent-dark/20 dark:text-notely-accent-dark"
            />
          )}
          className="flex flex-wrap gap-2"
        />
      </div>
      
      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleAddTag(inputValue);
            }
          }}
          placeholder="Add tag..."
          className="w-full px-3 py-2 text-sm bg-notely-surface dark:bg-notely-surface-dark border border-notely-border dark:border-notely-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-notely-accent/20 dark:focus:ring-notely-accent-dark/20"
        />
        <button
          onClick={() => handleAddTag(inputValue)}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-notely-accent/10 dark:hover:bg-notely-accent-dark/20 transition-colors"
          aria-label="Add tag"
        >
          <svg className="w-4 h-4 text-notely-accent dark:text-notely-accent-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>
    </div>
  );
};
```

### CategoriesPanel Component

```jsx
const CategoriesPanel = ({ categories, onAddCategory, onRemoveCategory, onUpdateCategories, allCategories, maxCategories }) => {
  const [inputValue, setInputValue] = useState('');
  const [message, setMessage] = useState(null);
  
  const handleAddCategory = (category) => {
    if (!category) return;
    
    const formattedCategory = formatForStorage(category);
    if (categories.includes(formattedCategory)) {
      setMessage({ type: 'error', text: 'Category already exists' });
      setTimeout(() => setMessage(null), 3000);
      return;
    }
    
    if (maxCategories && categories.length >= maxCategories) {
      setMessage({ type: 'error', text: `Maximum ${maxCategories} categories allowed` });
      setTimeout(() => setMessage(null), 3000);
      return;
    }
    
    onAddCategory(formattedCategory);
    setInputValue('');
  };
  
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-notely-text-primary dark:text-notely-text-primary-dark">
          Categories
        </h3>
        {message && (
          <InlineFeedback
            type={message.type}
            message={message.text}
            isVisible={!!message}
            className="text-xs py-1"
          />
        )}
      </div>
      
      <div className="flex flex-wrap gap-2 mb-2">
        <AnimatedList
          items={categories || []}
          keyExtractor={(category) => category}
          renderItem={(category) => (
            <Chip
              text={formatForDisplay(category)}
              onRemove={() => onRemoveCategory(category)}
              className="bg-notely-surface dark:bg-notely-surface-dark border border-notely-border dark:border-notely-border-dark"
            />
          )}
          className="flex flex-wrap gap-2"
        />
      </div>
      
      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleAddCategory(inputValue);
            }
          }}
          placeholder="Add category..."
          className="w-full px-3 py-2 text-sm bg-notely-surface dark:bg-notely-surface-dark border border-notely-border dark:border-notely-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-notely-accent/20 dark:focus:ring-notely-accent-dark/20"
        />
        <button
          onClick={() => handleAddCategory(inputValue)}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-notely-accent/10 dark:hover:bg-notely-accent-dark/20 transition-colors"
          aria-label="Add category"
        >
          <svg className="w-4 h-4 text-notely-accent dark:text-notely-accent-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>
    </div>
  );
};
```

### NotesEditor Component

```jsx
const NotesEditor = ({ notes, onSave, isSaving, saveNotification }) => {
  const [localNotes, setLocalNotes] = useState(notes || '');
  
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-notely-text-primary dark:text-notely-text-primary-dark">
          Notes
        </h3>
        {saveNotification && (
          <InlineFeedback
            type="success"
            message={saveNotification}
            isVisible={!!saveNotification}
            className="text-xs py-1"
          />
        )}
      </div>
      
      <textarea
        value={localNotes}
        onChange={(e) => setLocalNotes(e.target.value)}
        placeholder="Add your notes here..."
        className="w-full px-3 py-2 min-h-[100px] text-sm bg-notely-surface dark:bg-notely-surface-dark border border-notely-border dark:border-notely-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-notely-accent/20 dark:focus:ring-notely-accent-dark/20 resize-y"
      />
      
      <div className="flex justify-end">
        <button
          onClick={() => onSave(localNotes)}
          disabled={isSaving}
          className="px-3 py-1.5 text-sm bg-notely-accent text-white dark:bg-notely-accent-dark rounded-md hover:bg-notely-accent/90 dark:hover:bg-notely-accent-dark/90 focus:outline-none focus:ring-2 focus:ring-notely-accent/20 dark:focus:ring-notely-accent-dark/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isSaving ? 'Saving...' : 'Save Notes'}
        </button>
      </div>
    </div>
  );
};
```

### Main PostViewerFullScreen Component

```jsx
const PostViewerFullScreen = ({
  post,
  onClose,
  onAddTag,
  onRemoveTag,
  onAddCategory,
  onRemoveCategory,
  onUpdateNotes,
  onUpdateCategories,
  onUpdateTags,
  threadPosts
}) => {
  const [localPost, setLocalPost] = useState(post);
  const [notes, setNotes] = useState(post?.notes || '');
  const [isSaving, setIsSaving] = useState(false);
  const [saveNotification, setSaveNotification] = useState('');
  const [allCategories, setAllCategories] = useState([]);
  const [allTags, setAllTags] = useState([]);
  const [isClosing, setIsClosing] = useState(false);
  const { t } = useTranslation();
  
  // Load categories and tags
  useEffect(() => {
    const loadData = async () => {
      const categories = await getAllCategories();
      const tags = await getAllTags();
      setAllCategories(categories);
      setAllTags(tags);
    };
    
    loadData();
  }, []);
  
  // Handle close with animation
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
    }, 300);
  };
  
  // Handle save notes
  const handleSaveNotes = async (newNotes) => {
    setIsSaving(true);
    try {
      await updatePostDetails(localPost.id, { notes: newNotes });
      setNotes(newNotes);
      setLocalPost(prev => ({ ...prev, notes: newNotes }));
      
      if (onUpdateNotes) {
        onUpdateNotes(localPost.id, newNotes);
      }
      
      setSaveNotification(t('postViewer.notesSaved'));
      setTimeout(() => setSaveNotification(''), 3000);
    } catch (error) {
      console.error('Failed to save notes:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Handle tag operations
  const handleAddTag = async (tag) => {
    if (!localPost) return;
    
    const currentTags = localPost.tags || [];
    const newTags = [...currentTags, tag];
    
    try {
      await updatePostDetails(localPost.id, { tags: newTags });
      setLocalPost(prev => ({ ...prev, tags: newTags }));
      
      if (onUpdateTags) {
        onUpdateTags(localPost.id, newTags);
      }
    } catch (error) {
      console.error('Failed to add tag:', error);
    }
  };
  
  const handleRemoveTag = async (tag) => {
    if (!localPost) return;
    
    const currentTags = localPost.tags || [];
    const newTags = currentTags.filter(t => t !== tag);
    
    try {
      await updatePostDetails(localPost.id, { tags: newTags });
      setLocalPost(prev => ({ ...prev, tags: newTags }));
      
      if (onUpdateTags) {
        onUpdateTags(localPost.id, newTags);
      }
    } catch (error) {
      console.error('Failed to remove tag:', error);
    }
  };
  
  // Handle category operations
  const handleAddCategory = async (category) => {
    if (!localPost) return;
    
    const currentCategories = localPost.categories || [];
    const newCategories = [...currentCategories, category];
    
    try {
      await updatePostDetails(localPost.id, { categories: newCategories });
      setLocalPost(prev => ({ ...prev, categories: newCategories }));
      
      if (onUpdateCategories) {
        onUpdateCategories(localPost.id, newCategories);
      }
    } catch (error) {
      console.error('Failed to add category:', error);
    }
  };
  
  const handleRemoveCategory = async (category) => {
    if (!localPost) return;
    
    const currentCategories = localPost.categories || [];
    const newCategories = currentCategories.filter(c => c !== category);
    
    try {
      await updatePostDetails(localPost.id, { categories: newCategories });
      setLocalPost(prev => ({ ...prev, categories: newCategories }));
      
      if (onUpdateCategories) {
        onUpdateCategories(localPost.id, newCategories);
      }
    } catch (error) {
      console.error('Failed to remove category:', error);
    }
  };
  
  if (!localPost) return null;
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 dark:bg-black/70"
      onClick={handleClose}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.3 }}
        className={`relative w-full max-w-6xl max-h-[90vh] bg-notely-bg dark:bg-notely-bg-dark rounded-lg shadow-xl overflow-hidden ${isClosing ? 'animate-fadeOut' : ''}`}
        onClick={e => e.stopPropagation()}
      >
        <PostViewerHeader post={localPost} onClose={handleClose} />
        
        <div className="flex flex-col md:flex-row h-[calc(90vh-64px)]">
          {/* Content Column */}
          <div className="flex-1 overflow-auto p-6">
            {localPost.mediaType === 'image' && localPost.mediaUrl && (
              <div className="mb-6 flex justify-center">
                <ProxyImage
                  src={localPost.mediaUrl}
                  alt="Post image"
                  className="max-w-full max-h-[50vh] object-contain rounded-lg"
                />
              </div>
            )}
            
            {localPost.media && localPost.media.length > 1 && (
              <div className="mb-6">
                <ImageSwiper media={localPost.media} />
              </div>
            )}
            
            <div className="prose prose-sm dark:prose-invert max-w-none">
              <PostContentRenderer content={localPost.text || localPost.content || ''} />
            </div>
          </div>
          
          {/* Sidebar Column */}
          <div className="w-full md:w-80 flex-shrink-0 border-t md:border-t-0 md:border-l border-notely-border dark:border-notely-border-dark overflow-auto">
            <div className="p-6 space-y-6">
              {/* Insight Section */}
              {localPost.inSight && (
                <div className="bg-notely-card dark:bg-notely-card-dark rounded-lg p-4">
                  <h3 className="text-sm font-medium text-notely-text-primary dark:text-notely-text-primary-dark mb-2">
                    Insight
                  </h3>
                  <div className="flex items-center mb-2">
                    <span className="text-2xl mr-2">{localPost.inSight.emoji}</span>
                    <span className="text-sm text-notely-text-secondary dark:text-notely-text-secondary-dark">
                      {localPost.inSight.sentiment}
                    </span>
                  </div>
                  {localPost.fastTake && (
                    <p className="text-sm text-notely-text-primary dark:text-notely-text-primary-dark">
                      {localPost.fastTake}
                    </p>
                  )}
                </div>
              )}
              
              {/* Tags Panel */}
              <TagsPanel
                tags={localPost.tags || []}
                onAddTag={handleAddTag}
                onRemoveTag={handleRemoveTag}
                onUpdateTags={(tags) => onUpdateTags && onUpdateTags(localPost.id, tags)}
                allTags={allTags}
                maxTags={10}
              />
              
              {/* Categories Panel */}
              <CategoriesPanel
                categories={localPost.categories || []}
                onAddCategory={handleAddCategory}
                onRemoveCategory={handleRemoveCategory}
                onUpdateCategories={(categories) => onUpdateCategories && onUpdateCategories(localPost.id, categories)}
                allCategories={allCategories}
                maxCategories={5}
              />
              
              {/* Notes Editor */}
              <NotesEditor
                notes={notes}
                onSave={handleSaveNotes}
                isSaving={isSaving}
                saveNotification={saveNotification}
              />
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};
```

## Accessibility Considerations

To ensure the component is accessible:

1. All interactive elements will have appropriate ARIA attributes
2. Color contrast will meet WCAG AA standards
3. Keyboard navigation will be fully supported
4. Focus management will be implemented for modal behavior
5. Animations will respect the "prefers-reduced-motion" setting

## Performance Considerations

To maintain optimal performance:

1. The component will use virtualization for long lists of tags or categories
2. Images will be lazy-loaded
3. Animations will use CSS transforms and opacity for better performance
4. State updates will be batched to minimize re-renders
5. Heavy operations will be debounced or throttled