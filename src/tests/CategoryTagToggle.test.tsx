/**
 * Tests for CategoryTagToggle component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryTagToggle from '../components/CategoryTagToggle';

// Mock the translation hook
jest.mock('../hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'postViewer.categories': 'Categories',
        'postViewer.tags': 'Tags',
        'postViewer.noCategories': 'No categories.',
        'postViewer.noTags': 'No tags.',
      };
      return translations[key] || key;
    }
  })
}));

describe('CategoryTagToggle', () => {
  const defaultProps = {
    categories: ['Technology', 'AI'],
    tags: ['react', 'typescript'],
    allCategories: ['Technology', 'AI', 'Science', 'Programming'],
    allTags: ['react', 'typescript', 'javascript', 'web-dev'],
    onCategoriesChange: jest.fn(),
    onTagsChange: jest.fn(),
    maxCategories: 3,
    maxTags: 5,
    canEdit: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders with categories tab active by default', () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    expect(screen.getByText('Categories')).toBeInTheDocument();
    expect(screen.getByText('Tags')).toBeInTheDocument();
    expect(screen.getByText('Technology')).toBeInTheDocument();
    expect(screen.getByText('AI')).toBeInTheDocument();
  });

  test('switches to tags tab when clicked', () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    fireEvent.click(screen.getByText('Tags'));
    
    expect(screen.getByText('react')).toBeInTheDocument();
    expect(screen.getByText('typescript')).toBeInTheDocument();
  });

  test('shows add button when can edit and under max items', () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    expect(screen.getByText('+ Add')).toBeInTheDocument();
  });

  test('does not show add button when cannot edit', () => {
    render(<CategoryTagToggle {...defaultProps} canEdit={false} />);
    
    expect(screen.queryByText('+ Add')).not.toBeInTheDocument();
  });

  test('shows remaining count', () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    // Categories: 2 items, max 3, so 1 remaining
    expect(screen.getByText('1 remaining')).toBeInTheDocument();
  });

  test('shows empty state when no items', () => {
    render(<CategoryTagToggle {...defaultProps} categories={[]} />);
    
    expect(screen.getByText('No categories.')).toBeInTheDocument();
  });

  test('calls onCategoriesChange when removing a category', () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    const removeButtons = screen.getAllByText('×');
    fireEvent.click(removeButtons[0]);
    
    expect(defaultProps.onCategoriesChange).toHaveBeenCalledWith(['AI']);
  });

  test('calls onTagsChange when removing a tag', () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    // Switch to tags tab
    fireEvent.click(screen.getByText('Tags'));
    
    const removeButtons = screen.getAllByText('×');
    fireEvent.click(removeButtons[0]);
    
    expect(defaultProps.onTagsChange).toHaveBeenCalledWith(['typescript']);
  });

  test('shows input field when add button is clicked', async () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    fireEvent.click(screen.getByText('+ Add'));
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Add category...')).toBeInTheDocument();
    });
  });

  test('adds new item when typing and pressing enter', async () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    fireEvent.click(screen.getByText('+ Add'));
    
    const input = await screen.findByPlaceholderText('Add category...');
    fireEvent.change(input, { target: { value: 'New Category' } });
    fireEvent.keyDown(input, { key: 'Enter' });
    
    expect(defaultProps.onCategoriesChange).toHaveBeenCalledWith(['Technology', 'AI', 'New Category']);
  });

  test('shows success message when provided', () => {
    const props = {
      ...defaultProps,
      categoriesMessage: { text: 'Categories updated successfully', type: 'success' as const }
    };
    
    render(<CategoryTagToggle {...props} />);
    
    expect(screen.getByText('Categories updated successfully')).toBeInTheDocument();
  });

  test('shows error message when provided', () => {
    const props = {
      ...defaultProps,
      categoriesMessage: { text: 'Failed to update categories', type: 'error' as const }
    };
    
    render(<CategoryTagToggle {...props} />);
    
    expect(screen.getByText('Failed to update categories')).toBeInTheDocument();
  });

  test('does not add duplicate items', async () => {
    render(<CategoryTagToggle {...defaultProps} />);
    
    fireEvent.click(screen.getByText('+ Add'));
    
    const input = await screen.findByPlaceholderText('Add category...');
    fireEvent.change(input, { target: { value: 'Technology' } }); // Already exists
    fireEvent.keyDown(input, { key: 'Enter' });
    
    // Should not call onCategoriesChange since it's a duplicate
    expect(defaultProps.onCategoriesChange).not.toHaveBeenCalled();
  });

  test('respects max items limit', () => {
    const props = {
      ...defaultProps,
      categories: ['Cat1', 'Cat2', 'Cat3'], // Already at max
      maxCategories: 3
    };
    
    render(<CategoryTagToggle {...props} />);
    
    expect(screen.queryByText('+ Add')).not.toBeInTheDocument();
    expect(screen.getByText('0 remaining')).toBeInTheDocument();
  });
});
