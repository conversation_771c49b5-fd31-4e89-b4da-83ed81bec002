@import './styles/notely-theme.css';
@import './styles/post-card-fixes.css';
@import './styles/animations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Masonry layout for cards */
.masonry-grid {
  column-gap: 1.5rem;
  column-fill: initial;
}

/* Responsive column counts */
.masonry-grid {
  column-count: 1; /* Mobile */
}

@media (min-width: 768px) {
  .masonry-grid {
    column-count: 2; /* Tablet */
  }
}

@media (min-width: 1280px) {
  .masonry-grid {
    column-count: 3; /* Desktop */
  }
}

/* Ensure each card takes up full width of its column */
.masonry-item {
  break-inside: avoid;
  margin-bottom: 1.5rem;
  display: inline-block;
  width: 100%;
}

/* Fade-in animation for Inspire-To-Do cards */
.animate-fade-in {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.4s ease-out forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* User info alignment fixes */
/* Fix the vertical alignment between avatar, name and handle */
.flex-shrink-0 + .flex-grow {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 2.5rem; /* Match sm:h-10 avatar height */
}

/* Tighten spacing between name and handle */
.flex-col.space-y-1 {
  margin: 0;
  padding: 0;
  gap: 0.125rem; /* Very tight spacing */
}

/* Adjust line heights for better vertical rhythm */
.author-name {
  line-height: 1.2;
  margin-bottom: 0;
}

.author-handle {
  line-height: 1.2;
  margin-top: 0;
}


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
