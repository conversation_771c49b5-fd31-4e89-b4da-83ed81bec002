/**
 * Animation utilities for consistent animations across the application
 * This file provides reusable animation variants and utility functions
 * for working with animations in a performance and accessibility-friendly way.
 */

// Types for animation variants
export interface AnimationVariants {
  initial: object;
  animate: object;
  exit?: object;
}

// Type for transition animations
export type TransitionType = 'fade' | 'slide' | 'scale' | 'none';

// Common animation variants
export const fadeIn: AnimationVariants = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.2 } },
  exit: { opacity: 0, transition: { duration: 0.15 } }
};

export const slideUp: AnimationVariants = {
  initial: { y: 10, opacity: 0 },
  animate: { y: 0, opacity: 1, transition: { duration: 0.2 } },
  exit: { y: 10, opacity: 0, transition: { duration: 0.15 } }
};

export const slideDown: AnimationVariants = {
  initial: { y: -10, opacity: 0 },
  animate: { y: 0, opacity: 1, transition: { duration: 0.2 } },
  exit: { y: -10, opacity: 0, transition: { duration: 0.15 } }
};

export const slideRight: AnimationVariants = {
  initial: { x: -10, opacity: 0 },
  animate: { x: 0, opacity: 1, transition: { duration: 0.2 } },
  exit: { x: -10, opacity: 0, transition: { duration: 0.15 } }
};

export const slideLeft: AnimationVariants = {
  initial: { x: 10, opacity: 0 },
  animate: { x: 0, opacity: 1, transition: { duration: 0.2 } },
  exit: { x: 10, opacity: 0, transition: { duration: 0.15 } }
};

export const scale: AnimationVariants = {
  initial: { scale: 0.95, opacity: 0 },
  animate: { scale: 1, opacity: 1, transition: { duration: 0.2 } },
  exit: { scale: 0.95, opacity: 0, transition: { duration: 0.15 } }
};

export const modalVariants: AnimationVariants = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { 
    opacity: 1, 
    scale: 1, 
    transition: { 
      type: "spring", 
      damping: 25, 
      stiffness: 300 
    } 
  },
  exit: { 
    opacity: 0, 
    scale: 0.95, 
    transition: { 
      duration: 0.15 
    } 
  }
};

export const backdropVariants: AnimationVariants = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.2 } },
  exit: { opacity: 0, transition: { duration: 0.2 } }
};

export const listItemVariants: AnimationVariants = {
  initial: { opacity: 0, y: 5 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.2 } },
  exit: { opacity: 0, height: 0, transition: { duration: 0.2 } }
};

// Page transition variants
export const pageTransitionVariants: AnimationVariants = {
  initial: { opacity: 0, y: 10 },
  animate: { 
    opacity: 1, 
    y: 0, 
    transition: { 
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1] // Material Design standard easing
    } 
  },
  exit: { 
    opacity: 0, 
    y: -10, 
    transition: { 
      duration: 0.2,
      ease: [0.4, 0.0, 1, 1] // Accelerate easing for exit
    } 
  }
};

// Slide animation variants for page transitions
export const slideTransitionVariants: AnimationVariants = {
  initial: { opacity: 0, x: 20 },
  animate: { 
    opacity: 1, 
    x: 0, 
    transition: { 
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1]
    } 
  },
  exit: { 
    opacity: 0, 
    x: -20, 
    transition: { 
      duration: 0.2,
      ease: [0.4, 0.0, 1, 1]
    } 
  }
};

// Fade animation variants for page transitions
export const fadeTransitionVariants: AnimationVariants = {
  initial: { opacity: 0 },
  animate: { 
    opacity: 1, 
    transition: { 
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1]
    } 
  },
  exit: { 
    opacity: 0, 
    transition: { 
      duration: 0.2,
      ease: [0.4, 0.0, 1, 1]
    } 
  }
};

// Scale animation variants for page transitions
export const scaleTransitionVariants: AnimationVariants = {
  initial: { opacity: 0, scale: 0.97 },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: { 
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1]
    } 
  },
  exit: { 
    opacity: 0, 
    scale: 1.03,
    transition: { 
      duration: 0.2,
      ease: [0.4, 0.0, 1, 1]
    } 
  }
};

// Animation timing constants (in seconds)
export const ANIMATION_DURATION = {
  fast: 0.15,
  normal: 0.2,
  slow: 0.3
};

// Animation easing presets
export const ANIMATION_EASE = {
  default: [0.4, 0.0, 0.2, 1], // Material Design standard easing
  accelerate: [0.4, 0.0, 1, 1], // Accelerate easing
  decelerate: [0.0, 0.0, 0.2, 1], // Decelerate easing
  sharp: [0.4, 0.0, 0.6, 1] // Sharp easing
};

// Function to check if user prefers reduced motion
export const prefersReducedMotion = (): boolean => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Function to get appropriate animation based on user preference
export const getAnimationVariant = (variant: AnimationVariants): AnimationVariants => {
  if (prefersReducedMotion()) {
    // Return simplified animation that only changes opacity
    return {
      initial: { opacity: 0 },
      animate: { opacity: 1, transition: { duration: 0.1 } },
      exit: { opacity: 0, transition: { duration: 0.1 } }
    };
  }
  return variant;
};

// Function to create staggered children animations
export const createStaggeredChildrenVariants = (staggerAmount: number = 0.05): {
  container: AnimationVariants;
  item: AnimationVariants;
} => {
  return {
    container: {
      initial: {},
      animate: {
        transition: {
          staggerChildren: staggerAmount
        }
      },
      exit: {
        transition: {
          staggerChildren: staggerAmount / 2,
          staggerDirection: -1
        }
      }
    },
    item: {
      initial: { opacity: 0, y: 10 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: 10 }
    }
  };
};

// Utility function to create spring transition
export const springTransition = (damping: number = 25, stiffness: number = 300) => ({
  type: "spring",
  damping,
  stiffness
});

// Utility function to create tween transition
export const tweenTransition = (duration: number = ANIMATION_DURATION.normal, ease: number[] = ANIMATION_EASE.default) => ({
  type: "tween",
  duration,
  ease
});