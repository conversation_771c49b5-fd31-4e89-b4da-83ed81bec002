import { useState, useEffect } from 'react';
import { prefersReducedMotion } from '../utils/animationUtils';

interface UseAnimationOptions {
  duration?: number;
  delay?: number;
  respectReducedMotion?: boolean;
}

/**
 * Custom hook for managing animation states
 * @param initialState - Initial animation state (default: false)
 * @param options - Animation options
 * @returns Animation state and control functions
 */
export const useAnimation = (
  initialState: boolean = false,
  options: UseAnimationOptions = {}
) => {
  const { 
    duration = 200, 
    delay = 0, 
    respectReducedMotion = true 
  } = options;
  
  const [isAnimating, setIsAnimating] = useState(initialState);
  const [shouldRender, setShouldRender] = useState(initialState);
  const reducedMotion = respectReducedMotion && prefersReducedMotion();
  
  // Adjusted timing for reduced motion
  const actualDuration = reducedMotion ? Math.min(duration, 100) : duration;
  const actualDelay = reducedMotion ? Math.min(delay, 50) : delay;

  useEffect(() => {
    let animationTimer: ReturnType<typeof setTimeout>;
    let renderTimer: ReturnType<typeof setTimeout>;

    if (isAnimating) {
      // Start rendering immediately when animation starts
      setShouldRender(true);
    } else if (shouldRender) {
      // When animation ends, wait for it to complete before removing from DOM
      animationTimer = setTimeout(() => {
        setShouldRender(false);
      }, actualDuration);
    }

    return () => {
      clearTimeout(animationTimer);
      clearTimeout(renderTimer);
    };
  }, [isAnimating, shouldRender, actualDuration]);

  const start = () => {
    setShouldRender(true);
    // Small delay to ensure DOM is ready before animation starts
    setTimeout(() => setIsAnimating(true), 10);
  };

  const stop = () => {
    setIsAnimating(false);
  };

  const toggle = () => {
    if (isAnimating) {
      stop();
    } else {
      start();
    }
  };

  const animateWithDelay = () => {
    setTimeout(start, actualDelay);
  };

  return {
    isAnimating,
    shouldRender,
    start,
    stop,
    toggle,
    animateWithDelay
  };
};

/**
 * Custom hook for handling element visibility with animations
 * @param initialVisibility - Initial visibility state (default: false)
 * @param options - Animation options
 * @returns Visibility state and control functions
 */
export const useAnimatedVisibility = (
  initialVisibility: boolean = false,
  options: UseAnimationOptions = {}
) => {
  const { 
    duration = 200, 
    delay = 0, 
    respectReducedMotion = true 
  } = options;
  
  const [isVisible, setIsVisible] = useState(initialVisibility);
  const [shouldRender, setShouldRender] = useState(initialVisibility);
  const reducedMotion = respectReducedMotion && prefersReducedMotion();
  
  // Adjusted timing for reduced motion
  const actualDuration = reducedMotion ? Math.min(duration, 100) : duration;

  useEffect(() => {
    let hideTimer: ReturnType<typeof setTimeout>;

    if (isVisible) {
      // Start rendering immediately when becoming visible
      setShouldRender(true);
    } else if (shouldRender) {
      // When hiding, wait for animation to complete before removing from DOM
      hideTimer = setTimeout(() => {
        setShouldRender(false);
      }, actualDuration);
    }

    return () => {
      clearTimeout(hideTimer);
    };
  }, [isVisible, shouldRender, actualDuration]);

  const show = () => {
    setShouldRender(true);
    // Small delay to ensure DOM is ready before animation starts
    setTimeout(() => setIsVisible(true), 10);
  };

  const hide = () => {
    setIsVisible(false);
  };

  const toggle = () => {
    if (isVisible) {
      hide();
    } else {
      show();
    }
  };

  const showWithDelay = () => {
    setTimeout(show, delay);
  };

  return {
    isVisible,
    shouldRender,
    show,
    hide,
    toggle,
    showWithDelay
  };
};