import { useState, useEffect, useCallback } from 'react';
import { TransitionType } from '../components/ui/PageTransition';

interface RouteTransitionOptions {
  /**
   * The type of transition animation to use
   */
  transitionType?: TransitionType;
  
  /**
   * Duration of the exit animation in milliseconds
   */
  exitDuration?: number;
  
  /**
   * Callback function to execute before navigation
   */
  onBeforeNavigate?: () => void;
  
  /**
   * Callback function to execute after navigation
   */
  onAfterNavigate?: () => void;
}

interface RouteTransitionResult {
  /**
   * Current route key used for animations
   */
  routeKey: string;
  
  /**
   * Whether the route is currently transitioning
   */
  isTransitioning: boolean;
  
  /**
   * Function to navigate to a new route with transition
   */
  navigateTo: (path: string) => void;
  
  /**
   * The current transition type
   */
  transitionType: TransitionType;
  
  /**
   * Function to change the transition type
   */
  setTransitionType: (type: TransitionType) => void;
}

/**
 * Hook for managing route transitions
 * 
 * @param currentPath - The current route path
 * @param navigate - Navigation function (from router)
 * @param options - Configuration options
 * @returns Route transition state and controls
 */
export const useRouteTransition = (
  currentPath: string,
  navigate: (path: string) => void,
  options: RouteTransitionOptions = {}
): RouteTransitionResult => {
  const {
    transitionType: initialTransitionType = 'fade',
    exitDuration = 200,
    onBeforeNavigate,
    onAfterNavigate
  } = options;
  
  const [routeKey, setRouteKey] = useState<string>(currentPath);
  const [nextRoute, setNextRoute] = useState<string | null>(null);
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false);
  const [transitionType, setTransitionType] = useState<TransitionType>(initialTransitionType);
  
  // Handle navigation with transition
  const navigateTo = useCallback((path: string) => {
    if (path === currentPath) return;
    
    // Call before navigate callback
    onBeforeNavigate?.();
    
    // Start transition
    setIsTransitioning(true);
    setNextRoute(path);
    
    // Update route key to trigger exit animation
    setRouteKey(`${currentPath}-exiting`);
  }, [currentPath, onBeforeNavigate]);
  
  // Handle actual navigation after exit animation completes
  const handleExitComplete = useCallback(() => {
    if (nextRoute) {
      // Perform actual navigation
      navigate(nextRoute);
      
      // Reset state
      setNextRoute(null);
      setIsTransitioning(false);
      
      // Update route key for the new route
      setRouteKey(nextRoute);
      
      // Call after navigate callback
      onAfterNavigate?.();
    }
  }, [nextRoute, navigate, onAfterNavigate]);
  
  // Set up exit animation completion handler
  useEffect(() => {
    if (isTransitioning && nextRoute) {
      const timer = setTimeout(() => {
        handleExitComplete();
      }, exitDuration);
      
      return () => clearTimeout(timer);
    }
  }, [isTransitioning, nextRoute, exitDuration, handleExitComplete]);
  
  // Update route key when path changes externally
  useEffect(() => {
    if (!isTransitioning) {
      setRouteKey(currentPath);
    }
  }, [currentPath, isTransitioning]);
  
  return {
    routeKey,
    isTransitioning,
    navigateTo,
    transitionType,
    setTransitionType
  };
};

/**
 * A simpler version of useRouteTransition for components that don't use a router
 * 
 * @param initialView - The initial active view
 * @param options - Configuration options
 * @returns View transition state and controls
 */
export const useViewTransition = (
  initialView: string,
  options: RouteTransitionOptions = {}
): {
  activeView: string;
  isTransitioning: boolean;
  changeView: (view: string) => void;
  transitionType: TransitionType;
  setTransitionType: (type: TransitionType) => void;
} => {
  const [activeView, setActiveView] = useState<string>(initialView);
  const [nextView, setNextView] = useState<string | null>(null);
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false);
  const [transitionType, setTransitionType] = useState<TransitionType>(
    options.transitionType || 'fade'
  );
  
  const changeView = useCallback((view: string) => {
    if (view === activeView) return;
    
    options.onBeforeNavigate?.();
    setIsTransitioning(true);
    setNextView(view);
  }, [activeView, options]);
  
  const handleTransitionComplete = useCallback(() => {
    if (nextView) {
      setActiveView(nextView);
      setNextView(null);
      setIsTransitioning(false);
      options.onAfterNavigate?.();
    }
  }, [nextView, options]);
  
  useEffect(() => {
    if (isTransitioning && nextView) {
      const timer = setTimeout(() => {
        handleTransitionComplete();
      }, options.exitDuration || 200);
      
      return () => clearTimeout(timer);
    }
  }, [isTransitioning, nextView, options.exitDuration, handleTransitionComplete]);
  
  return {
    activeView,
    isTransitioning,
    changeView,
    transitionType,
    setTransitionType
  };
};