import { useState, useEffect } from 'react';
import { TransitionType } from '../components/ui/PageTransition';
import { getAnimationSettings, DEFAULT_ANIMATION_SETTINGS } from '../styles/animation-classes';

/**
 * Custom hook for managing application animations based on user settings
 * @returns Animation settings and helper functions
 */
export const useAppAnimations = () => {
  const [transitionType, setTransitionType] = useState<TransitionType>(DEFAULT_ANIMATION_SETTINGS.transitionType);
  const [animationsEnabled, setAnimationsEnabled] = useState<boolean>(DEFAULT_ANIMATION_SETTINGS.enabled);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  // Load animation settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true);
        const settings = await getAnimationSettings();
        setTransitionType(settings.transitionType);
        setAnimationsEnabled(settings.enabled);
      } catch (error) {
        console.error('Error loading animation settings:', error);
        // Use defaults if there's an error
        setTransitionType(DEFAULT_ANIMATION_SETTINGS.transitionType);
        setAnimationsEnabled(DEFAULT_ANIMATION_SETTINGS.enabled);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadSettings();
  }, []);
  
  // Get the effective transition type based on settings
  const getEffectiveTransitionType = (): TransitionType => {
    if (!animationsEnabled) {
      return 'none';
    }
    return transitionType;
  };
  
  return {
    transitionType: getEffectiveTransitionType(),
    animationsEnabled,
    isLoading
  };
};