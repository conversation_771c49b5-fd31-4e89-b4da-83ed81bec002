import React, { useState } from 'react';
import CategoryTagToggle from './CategoryTagToggle';

const CategoryTagToggleDemo: React.FC = () => {
  const [categories, setCategories] = useState<string[]>(['Technology', 'AI']);
  const [tags, setTags] = useState<string[]>(['React', 'TypeScript']);
  const [categoriesMessage, setCategoriesMessage] = useState<{text: string, type: 'success' | 'error'} | null>(null);
  const [tagsMessage, setTagsMessage] = useState<{text: string, type: 'success' | 'error'} | null>(null);

  const allCategories = ['Technology', 'AI', 'Science', 'Programming', 'Web Development', 'Machine Learning'];
  const allTags = ['React', 'TypeScript', 'JavaScript', 'CSS', 'HTML', 'Node.js', 'Python', 'AI', 'ML'];

  const handleCategoriesChange = (newCategories: string[]) => {
    setCategories(newCategories);
    setCategoriesMessage({ text: 'Categories updated successfully!', type: 'success' });
    setTimeout(() => setCategoriesMessage(null), 3000);
  };

  const handleTagsChange = (newTags: string[]) => {
    setTags(newTags);
    setTagsMessage({ text: 'Tags updated successfully!', type: 'success' });
    setTimeout(() => setTagsMessage(null), 3000);
  };

  return (
    <div className="min-h-screen bg-notely-bg p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white mb-2">
            CategoryTagToggle Component Demo
          </h1>
          <p className="text-zinc-400 mb-2">
            A compact, lightweight UI component that lets users toggle between Categories and Tags
          </p>
          <div className="inline-flex items-center gap-2 bg-zinc-800 px-3 py-1.5 rounded-full text-xs text-zinc-300">
            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
            Updated with premium compact design
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {/* Interactive Demo */}
          <div className="space-y-3">
            <h2 className="text-lg font-semibold text-notely-text-primary">Interactive Demo</h2>
            <CategoryTagToggle
              categories={categories}
              tags={tags}
              allCategories={allCategories}
              allTags={allTags}
              onCategoriesChange={handleCategoriesChange}
              onTagsChange={handleTagsChange}
              categoriesMessage={categoriesMessage}
              tagsMessage={tagsMessage}
              maxCategories={3}
              maxTags={5}
              canEdit={true}
            />
          </div>

          {/* Read-only Version */}
          <div className="space-y-3">
            <h2 className="text-lg font-semibold text-notely-text-primary">Read-only Version</h2>
            <CategoryTagToggle
              categories={['Design', 'UX/UI', 'Figma']}
              tags={['design-system', 'prototyping', 'user-research']}
              allCategories={[]}
              allTags={[]}
              onCategoriesChange={() => {}}
              onTagsChange={() => {}}
              maxCategories={3}
              maxTags={5}
              canEdit={false}
            />
          </div>

          {/* Compact Example with Many Items */}
          <div className="space-y-3">
            <h2 className="text-lg font-semibold text-notely-text-primary">Compact with Many Items</h2>
            <CategoryTagToggle
              categories={['Technology', 'AI', 'Programming']}
              tags={['react', 'typescript', 'javascript', 'css', 'html']}
              allCategories={allCategories}
              allTags={allTags}
              onCategoriesChange={() => {}}
              onTagsChange={() => {}}
              maxCategories={3}
              maxTags={5}
              canEdit={false}
            />
          </div>
        </div>

        {/* Current State Display */}
        <div className="bg-notely-card border border-notely-border/10 dark:border-notely-border-dark/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-notely-text-primary mb-4">Current State</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-notely-text-secondary mb-2">Categories ({categories.length}/3)</h4>
              <div className="bg-notely-surface rounded-lg p-3">
                <code className="text-sm text-notely-text-primary">
                  {JSON.stringify(categories, null, 2)}
                </code>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-notely-text-secondary mb-2">Tags ({tags.length}/5)</h4>
              <div className="bg-notely-surface rounded-lg p-3">
                <code className="text-sm text-notely-text-primary">
                  {JSON.stringify(tags, null, 2)}
                </code>
              </div>
            </div>
          </div>
        </div>

        {/* Features List */}
        <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">✨ Compact Design Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul className="space-y-2 text-sm text-zinc-300">
              <li>✅ Compact toggle tabs with zinc-800 background</li>
              <li>✅ Small pills with zinc-700 background & inner shadow</li>
              <li>✅ Reduced font sizes (text-xs, text-[10px])</li>
              <li>✅ Minimal padding (p-2.5) for lightweight feel</li>
              <li>✅ Max height with scroll for long lists</li>
            </ul>
            <ul className="space-y-2 text-sm text-zinc-300">
              <li>✅ Inline "+ Add" button stays with pills</li>
              <li>✅ Instant tab switching (no heavy transitions)</li>
              <li>✅ Compact dropdown suggestions</li>
              <li>✅ Premium sidebar tool aesthetic</li>
              <li>✅ Consistent zinc color palette</li>
            </ul>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="bg-notely-card border border-notely-border/10 dark:border-notely-border-dark/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-notely-text-primary mb-4">How to Use</h3>
          <div className="space-y-3 text-sm text-notely-text-secondary">
            <p><strong>Switch tabs:</strong> Click on "Categories" or "Tags" to switch between them</p>
            <p><strong>Add items:</strong> Click "+ Add" button, then type and press Enter or select from suggestions</p>
            <p><strong>Remove items:</strong> Click the × button next to any item</p>
            <p><strong>Keyboard shortcuts:</strong> Enter or comma to add, Escape to cancel, Backspace to remove last item</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryTagToggleDemo;
