import React, { useState } from 'react';
import CategoryTagToggle from './CategoryTagToggle';

const CategoryTagToggleDemo: React.FC = () => {
  const [categories, setCategories] = useState<string[]>(['Technology', 'AI']);
  const [tags, setTags] = useState<string[]>(['React', 'TypeScript']);
  const [categoriesMessage, setCategoriesMessage] = useState<{text: string, type: 'success' | 'error'} | null>(null);
  const [tagsMessage, setTagsMessage] = useState<{text: string, type: 'success' | 'error'} | null>(null);

  const allCategories = ['Technology', 'AI', 'Science', 'Programming', 'Web Development', 'Machine Learning'];
  const allTags = ['React', 'TypeScript', 'JavaScript', 'CSS', 'HTML', 'Node.js', 'Python', 'AI', 'ML'];

  const handleCategoriesChange = (newCategories: string[]) => {
    setCategories(newCategories);
    setCategoriesMessage({ text: 'Categories updated successfully!', type: 'success' });
    setTimeout(() => setCategoriesMessage(null), 3000);
  };

  const handleTagsChange = (newTags: string[]) => {
    setTags(newTags);
    setTagsMessage({ text: 'Tags updated successfully!', type: 'success' });
    setTimeout(() => setTagsMessage(null), 3000);
  };

  return (
    <div className="min-h-screen bg-notely-bg p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-notely-text-primary mb-2">
            CategoryTagToggle Component Demo
          </h1>
          <p className="text-notely-text-secondary">
            A clean, minimal UI component that lets users toggle between Categories and Tags
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Demo Component */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-notely-text-primary">Interactive Demo</h2>
            <CategoryTagToggle
              categories={categories}
              tags={tags}
              allCategories={allCategories}
              allTags={allTags}
              onCategoriesChange={handleCategoriesChange}
              onTagsChange={handleTagsChange}
              categoriesMessage={categoriesMessage}
              tagsMessage={tagsMessage}
              maxCategories={3}
              maxTags={5}
              canEdit={true}
            />
          </div>

          {/* Read-only Version */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-notely-text-primary">Read-only Version</h2>
            <CategoryTagToggle
              categories={['Design', 'UX/UI', 'Figma']}
              tags={['design-system', 'prototyping', 'user-research']}
              allCategories={[]}
              allTags={[]}
              onCategoriesChange={() => {}}
              onTagsChange={() => {}}
              maxCategories={3}
              maxTags={5}
              canEdit={false}
            />
          </div>
        </div>

        {/* Current State Display */}
        <div className="bg-notely-card border border-notely-border/10 dark:border-notely-border-dark/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-notely-text-primary mb-4">Current State</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-notely-text-secondary mb-2">Categories ({categories.length}/3)</h4>
              <div className="bg-notely-surface rounded-lg p-3">
                <code className="text-sm text-notely-text-primary">
                  {JSON.stringify(categories, null, 2)}
                </code>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-notely-text-secondary mb-2">Tags ({tags.length}/5)</h4>
              <div className="bg-notely-surface rounded-lg p-3">
                <code className="text-sm text-notely-text-primary">
                  {JSON.stringify(tags, null, 2)}
                </code>
              </div>
            </div>
          </div>
        </div>

        {/* Features List */}
        <div className="bg-notely-card border border-notely-border/10 dark:border-notely-border-dark/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-notely-text-primary mb-4">Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul className="space-y-2 text-sm text-notely-text-secondary">
              <li>✅ Toggle between Categories and Tags</li>
              <li>✅ Clean pill-based UI design</li>
              <li>✅ Add new items with autocomplete</li>
              <li>✅ Remove items with × button</li>
              <li>✅ Respects max item limits</li>
            </ul>
            <ul className="space-y-2 text-sm text-notely-text-secondary">
              <li>✅ Shows remaining count</li>
              <li>✅ Success/error message display</li>
              <li>✅ Keyboard navigation (Enter, Escape)</li>
              <li>✅ Read-only mode support</li>
              <li>✅ Dark/light theme compatible</li>
            </ul>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="bg-notely-card border border-notely-border/10 dark:border-notely-border-dark/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-notely-text-primary mb-4">How to Use</h3>
          <div className="space-y-3 text-sm text-notely-text-secondary">
            <p><strong>Switch tabs:</strong> Click on "Categories" or "Tags" to switch between them</p>
            <p><strong>Add items:</strong> Click "+ Add" button, then type and press Enter or select from suggestions</p>
            <p><strong>Remove items:</strong> Click the × button next to any item</p>
            <p><strong>Keyboard shortcuts:</strong> Enter or comma to add, Escape to cancel, Backspace to remove last item</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryTagToggleDemo;
