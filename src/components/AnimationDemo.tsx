import React, { useState } from "react";
import { AnimatedButton } from "./ui/AnimatedButton";
import { Animated<PERSON><PERSON>, <PERSON>Header, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, CardFooter } from "./ui/AnimatedCard";
import AnimatedModal from "./ui/AnimatedModal";
import { AnimatedNotification } from "./ui/AnimatedNotification";
import { AnimatedList, AnimatedGrid } from "./ui/AnimatedList";
import { AnimatedContainer } from "./ui/AnimatedContainer";
import { LoadingSpinner, LoadingDots } from "./ui/LoadingSpinner";
import { useAnimatedVisibility } from "../hooks/useAnimation";
import { AnimatedToggle, AnimatedToggleGroup } from "./ui/AnimatedToggle";
import { SlidePanel, CollapsiblePanel } from "./ui/SlidePanel";
import { AnimatedTabs } from "./ui/AnimatedTabs";
import { Skeleton, SkeletonText, SkeletonCard } from "./ui/Skeleton";
import { Progress, CircularProgress } from "./ui/Progress";
import { InlineFeedback, AnimatedInput } from "./ui/InlineFeedback";
import { StatusIndicator, StatusBadge } from "./ui/StatusIndicator";
import PageTransitionDemo from "./PageTransitionDemo";
import RouteTransitionDemo from "./RouteTransitionDemo";
import { TransitionType } from "./ui/PageTransition";

/**
 * A demo component showcasing the various animation components
 */
const AnimationDemo = () => {
  // State for controlling various animations
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [activeNotification, setActiveNotification] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [toggleState, setToggleState] = useState(false);
  const [toggleGroupValue, setToggleGroupValue] = useState("option1");
  const [isCollapsibleOpen, setIsCollapsibleOpen] = useState(false);
  const [progressValue, setProgressValue] = useState(30);
  
  // Use the animated visibility hook for a section
  const { isVisible: isExtraContentVisible, toggle: toggleExtraContent } = useAnimatedVisibility(false);
  
  // Sample data for lists
  const listItems = [
    { id: 1, title: "First Item", description: "This is the first item description" },
    { id: 2, title: "Second Item", description: "This is the second item description" },
    { id: 3, title: "Third Item", description: "This is the third item description" }
  ];
  
  // Sample data for grid
  const gridItems = [
    { id: 1, title: "Card 1", color: "bg-blue-100" },
    { id: 2, title: "Card 2", color: "bg-green-100" },
    { id: 3, title: "Card 3", color: "bg-yellow-100" },
    { id: 4, title: "Card 4", color: "bg-purple-100" },
    { id: 5, title: "Card 5", color: "bg-pink-100" },
    { id: 6, title: "Card 6", color: "bg-indigo-100" }
  ];
  
  // Handle button loading simulation
  const handleLoadingClick = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };
  
  // Show a notification
  const showNotification = (type: "success" | "error" | "warning" | "info") => {
    setActiveNotification(type);
    setTimeout(() => {
      setActiveNotification(null);
    }, 3000);
  };
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Animation Components Demo</h1>
      
      {/* Button Animations Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Button Animations</h2>
        <div className="flex flex-wrap gap-4">
          <AnimatedButton>Default Button</AnimatedButton>
          <AnimatedButton variant="secondary">Secondary Button</AnimatedButton>
          <AnimatedButton variant="outline">Outline Button</AnimatedButton>
          <AnimatedButton variant="ghost">Ghost Button</AnimatedButton>
          <AnimatedButton variant="link">Link Button</AnimatedButton>
          <AnimatedButton isLoading>Loading Button</AnimatedButton>
          <AnimatedButton 
            isLoading={isLoading} 
            onClick={handleLoadingClick}
            loadingText="Processing..."
          >
            Click to Load
          </AnimatedButton>
        </div>
      </section>
      
      {/* Card Animations Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Card Animations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <AnimatedCard interactive elevation="sm">
            <CardHeader>
              <CardTitle>Interactive Card</CardTitle>
            </CardHeader>
            <CardContent>
              <p>This card has hover and click animations.</p>
            </CardContent>
            <CardFooter>
              <AnimatedButton size="sm">Card Action</AnimatedButton>
            </CardFooter>
          </AnimatedCard>
          
          <AnimatedCard variant="outline" interactive>
            <CardHeader>
              <CardTitle>Outline Card</CardTitle>
            </CardHeader>
            <CardContent>
              <p>This card has an outline style.</p>
            </CardContent>
          </AnimatedCard>
          
          <AnimatedCard elevation="lg">
            <CardHeader>
              <CardTitle>Static Card</CardTitle>
            </CardHeader>
            <CardContent>
              <p>This card doesn't have interactive animations.</p>
            </CardContent>
          </AnimatedCard>
        </div>
      </section>
      
      {/* Modal Animation Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Modal Animation</h2>
        <AnimatedButton onClick={() => setIsModalOpen(true)}>
          Open Modal
        </AnimatedButton>
        
        <AnimatedModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Animated Modal"
          description="This modal has smooth entrance and exit animations"
        >
          <div className="space-y-4">
            <p>This is the content of the modal with smooth animations.</p>
            <p>The modal animates in and out with a spring animation.</p>
            <div className="flex justify-end">
              <AnimatedButton onClick={() => setIsModalOpen(false)}>
                Close Modal
              </AnimatedButton>
            </div>
          </div>
        </AnimatedModal>
      </section>
      
      {/* Notification Animations Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Notification Animations</h2>
        <div className="flex flex-wrap gap-4">
          <AnimatedButton onClick={() => showNotification("success")}>
            Success Notification
          </AnimatedButton>
          <AnimatedButton onClick={() => showNotification("error")}>
            Error Notification
          </AnimatedButton>
          <AnimatedButton onClick={() => showNotification("warning")}>
            Warning Notification
          </AnimatedButton>
          <AnimatedButton onClick={() => showNotification("info")}>
            Info Notification
          </AnimatedButton>
        </div>
        
        <AnimatedNotification
          type="success"
          title="Success"
          message="Operation completed successfully!"
          isVisible={activeNotification === "success"}
          onClose={() => setActiveNotification(null)}
          position="top-right"
        />
        
        <AnimatedNotification
          type="error"
          title="Error"
          message="Something went wrong. Please try again."
          isVisible={activeNotification === "error"}
          onClose={() => setActiveNotification(null)}
          position="top-right"
        />
        
        <AnimatedNotification
          type="warning"
          title="Warning"
          message="Please review your information before continuing."
          isVisible={activeNotification === "warning"}
          onClose={() => setActiveNotification(null)}
          position="top-right"
        />
        
        <AnimatedNotification
          type="info"
          title="Information"
          message="Here's something you might want to know."
          isVisible={activeNotification === "info"}
          onClose={() => setActiveNotification(null)}
          position="top-right"
        />
      </section>
      
      {/* Loading Animations Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Loading Animations</h2>
        <div className="flex flex-wrap gap-8 items-center">
          <div>
            <h3 className="text-sm font-medium mb-2">Spinner Sizes</h3>
            <div className="flex items-center gap-4">
              <LoadingSpinner size="xs" />
              <LoadingSpinner size="sm" />
              <LoadingSpinner size="md" />
              <LoadingSpinner size="lg" />
              <LoadingSpinner size="xl" />
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium mb-2">Spinner Colors</h3>
            <div className="flex items-center gap-4">
              <LoadingSpinner color="primary" />
              <LoadingSpinner color="secondary" />
              <LoadingSpinner color="gray" />
              <div className="bg-gray-800 p-2 rounded">
                <LoadingSpinner color="white" />
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium mb-2">Loading Dots</h3>
            <div className="flex items-center gap-4">
              <LoadingDots size="sm" />
              <LoadingDots size="md" />
              <LoadingDots size="lg" />
            </div>
          </div>
        </div>
      </section>
      
      {/* Animated Container Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Animated Container</h2>
        <AnimatedButton onClick={toggleExtraContent}>
          {isExtraContentVisible ? "Hide Content" : "Show Content"}
        </AnimatedButton>
        
        <AnimatedContainer
          isVisible={isExtraContentVisible}
          variants={{
            initial: { opacity: 0, height: 0 },
            animate: { opacity: 1, height: "auto" },
            exit: { opacity: 0, height: 0 }
          }}
          className="overflow-hidden"
        >
          <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <h3 className="font-medium mb-2">Extra Content</h3>
            <p>This content smoothly animates in and out when toggled.</p>
            <p className="mt-2">The height transition is handled automatically.</p>
          </div>
        </AnimatedContainer>
      </section>
      
      {/* Animated List Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Animated List</h2>
        <AnimatedList
          items={listItems}
          keyExtractor={(item) => item.id}
          renderItem={(item) => (
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
              <h3 className="font-medium">{item.title}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{item.description}</p>
            </div>
          )}
          className="space-y-3"
        />
      </section>
      
      {/* Animated Grid Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Animated Grid</h2>
        <AnimatedGrid
          items={gridItems}
          keyExtractor={(item) => item.id}
          renderItem={(item) => (
            <div className={`p-4 rounded-lg shadow ${item.color} dark:bg-opacity-20 h-32 flex items-center justify-center`}>
              <h3 className="font-medium text-center">{item.title}</h3>
            </div>
          )}
          columns={{ sm: 2, md: 3, lg: 3 }}
        />
      </section>
      
      {/* Toggle Animations Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Toggle Animations</h2>
        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Toggle Sizes</h3>
            <div className="flex flex-col space-y-4">
              <AnimatedToggle 
                isOn={toggleState} 
                onToggle={() => setToggleState(!toggleState)} 
                label="Small Toggle" 
                size="sm" 
              />
              <AnimatedToggle 
                isOn={toggleState} 
                onToggle={() => setToggleState(!toggleState)} 
                label="Medium Toggle" 
                size="md" 
              />
              <AnimatedToggle 
                isOn={toggleState} 
                onToggle={() => setToggleState(!toggleState)} 
                label="Large Toggle" 
                size="lg" 
              />
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Toggle Group</h3>
            <AnimatedToggleGroup
              options={[
                { value: "option1", label: "Daily" },
                { value: "option2", label: "Weekly" },
                { value: "option3", label: "Monthly" }
              ]}
              value={toggleGroupValue}
              onChange={setToggleGroupValue}
            />
          </div>
        </div>
      </section>
      
      {/* Slide Panel Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Slide Panel Animations</h2>
        <div className="flex flex-wrap gap-4">
          <AnimatedButton onClick={() => setIsPanelOpen(true)}>
            Open Slide Panel
          </AnimatedButton>
          
          <AnimatedButton onClick={() => setIsCollapsibleOpen(!isCollapsibleOpen)}>
            {isCollapsibleOpen ? "Close Collapsible" : "Open Collapsible"}
          </AnimatedButton>
        </div>
        
        <div className="mt-4">
          <CollapsiblePanel
            isOpen={isCollapsibleOpen}
            onToggle={() => setIsCollapsibleOpen(!isCollapsibleOpen)}
            title="Collapsible Panel Title"
          >
            <div className="space-y-2">
              <p>This is collapsible content that smoothly expands and collapses.</p>
              <p>The height animation is handled automatically.</p>
            </div>
          </CollapsiblePanel>
        </div>
        
        <SlidePanel
          isOpen={isPanelOpen}
          onClose={() => setIsPanelOpen(false)}
          position="right"
          title="Slide Panel"
        >
          <div className="p-4">
            <p className="mb-4">This panel slides in from the side with a smooth animation.</p>
            <p className="mb-4">You can configure it to slide from any edge of the screen.</p>
            <AnimatedButton onClick={() => setIsPanelOpen(false)}>
              Close Panel
            </AnimatedButton>
          </div>
        </SlidePanel>
      </section>
      
      {/* Tabs Animation Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Tab Animations</h2>
        <AnimatedTabs
          tabs={[
            {
              id: "tab1",
              label: "First Tab",
              content: (
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h3 className="font-medium mb-2">First Tab Content</h3>
                  <p>This content animates when switching between tabs.</p>
                </div>
              )
            },
            {
              id: "tab2",
              label: "Second Tab",
              content: (
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h3 className="font-medium mb-2">Second Tab Content</h3>
                  <p>Different content with the same smooth transition.</p>
                </div>
              )
            },
            {
              id: "tab3",
              label: "Third Tab",
              content: (
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h3 className="font-medium mb-2">Third Tab Content</h3>
                  <p>More content with consistent animations.</p>
                </div>
              )
            }
          ]}
          variant="pills"
        />
      </section>
      
      {/* Skeleton Loaders Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Skeleton Loaders</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Text Skeleton</h3>
            <SkeletonText lines={3} />
          </div>
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Card Skeleton</h3>
            <SkeletonCard />
          </div>
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Custom Skeleton</h3>
            <div className="flex items-center space-x-4">
              <Skeleton variant="circular" className="h-12 w-12" />
              <div className="space-y-2 flex-1">
                <Skeleton variant="text" className="h-4 w-3/4" />
                <Skeleton variant="text" className="h-3 w-1/2" />
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Progress Indicators Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Progress Indicators</h2>
        <div className="space-y-8">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Linear Progress</h3>
            <div className="space-y-6">
              <Progress 
                value={progressValue} 
                label="Basic Progress" 
                showValue 
              />
              
              <Progress 
                value={progressValue} 
                color="success" 
                size="lg" 
                label="Success Progress" 
                showValue 
              />
              
              <Progress 
                indeterminate 
                label="Indeterminate Progress" 
              />
            </div>
            
            <div className="mt-4">
              <AnimatedButton 
                onClick={() => setProgressValue(prev => (prev + 10) % 110)}
              >
                Increment Progress
              </AnimatedButton>
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Circular Progress</h3>
            <div className="flex flex-wrap gap-8">
              <CircularProgress 
                value={progressValue} 
                showValue 
              />
              
              <CircularProgress 
                value={progressValue} 
                size="lg" 
                color="success" 
                showValue 
              />
              
              <CircularProgress 
                indeterminate 
                size="md" 
                color="secondary" 
              />
            </div>
          </div>
        </div>
      </section>
      
      {/* Inline Feedback Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Inline Feedback Animations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Feedback Messages</h3>
            <div className="space-y-4">
              <InlineFeedback
                type="success"
                message="Your changes have been saved successfully."
                isVisible={true}
              />
              
              <InlineFeedback
                type="error"
                message="Please enter a valid email address."
                isVisible={true}
              />
              
              <InlineFeedback
                type="warning"
                message="Your session will expire in 5 minutes."
                isVisible={true}
              />
              
              <InlineFeedback
                type="info"
                message="You can add up to 5 items to your list."
                isVisible={true}
              />
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Animated Form Fields</h3>
            <div className="space-y-4">
              <AnimatedInput
                label="Valid Input"
                placeholder="Enter your name"
                validationState="valid"
                success="Looks good!"
              />
              
              <AnimatedInput
                label="Invalid Input"
                placeholder="Enter your email"
                validationState="invalid"
                error="Please enter a valid email address."
              />
              
              <AnimatedInput
                label="Normal Input"
                placeholder="Enter something"
              />
            </div>
          </div>
        </div>
      </section>
      
      {/* Status Indicators Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Status Indicators</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Status Dots</h3>
            <div className="space-y-4">
              <div className="flex flex-col space-y-3">
                <StatusIndicator status="online" label="Online" />
                <StatusIndicator status="offline" label="Offline" />
                <StatusIndicator status="away" label="Away" />
                <StatusIndicator status="busy" label="Busy" />
                <StatusIndicator status="loading" label="Loading" />
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Status Badges</h3>
            <div className="flex flex-wrap gap-3">
              <StatusBadge status="success" />
              <StatusBadge status="error" />
              <StatusBadge status="warning" />
              <StatusBadge status="info" />
              <StatusBadge status="loading" />
            </div>
            
            <h3 className="text-sm font-medium mt-6">Custom Labels</h3>
            <div className="flex flex-wrap gap-3">
              <StatusBadge status="success" label="Completed" />
              <StatusBadge status="error" label="Failed" />
              <StatusBadge status="warning" label="Pending" />
              <StatusBadge status="info" label="In Review" />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AnimationDemo;