import React, { useState, useEffect } from 'react';
import { PageTransition, TransitionType } from './ui/PageTransition';
import { RouteTransition } from './ui/RouteTransition';
import { AnimatedButton } from './ui/AnimatedButton';
import { <PERSON><PERSON><PERSON>, CardHeader, CardTitle, CardContent } from './ui/AnimatedCard';
import { useTranslation } from '../hooks/useTranslation';
import Toggle from './Toggle';
import { 
  DEFAULT_ANIMATION_SETTINGS, 
  getAnimationSettings, 
  saveAnimationSettings, 
  prefersReducedMotion 
} from '../styles/animation-classes';

// Sample page content components
const Page1 = () => (
  <div className="p-6 bg-notely-card rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">First Page</h2>
    <p className="mb-4">This is the content of the first page. It animates smoothly when switching between pages.</p>
    <p>The transition is handled by the PageTransition component.</p>
  </div>
);

const Page2 = () => (
  <div className="p-6 bg-notely-card rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">Second Page</h2>
    <p className="mb-4">Welcome to the second page! Notice how the content transitions smoothly.</p>
    <p>Different transition types can be applied to create various effects.</p>
  </div>
);

const Page3 = () => (
  <div className="p-6 bg-notely-card rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">Third Page</h2>
    <p className="mb-4">This is the third page in our demo.</p>
    <p>Page transitions improve the user experience by making navigation feel more natural and less abrupt.</p>
  </div>
);

/**
 * Animation Settings component for the settings page
 */
const AnimationSettings: React.FC = () => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [transitionType, setTransitionType] = useState<TransitionType>('fade');
  const [currentRoute, setCurrentRoute] = useState<string>('home');
  const [animationsEnabled, setAnimationsEnabled] = useState<boolean>(true);
  const [respectReducedMotion, setRespectReducedMotion] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveStatus, setSaveStatus] = useState<{show: boolean; message: string}>({
    show: false,
    message: ''
  });
  
  // Load animation settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      const settings = await getAnimationSettings();
      setTransitionType(settings.transitionType);
      setAnimationsEnabled(settings.enabled);
      setRespectReducedMotion(settings.reducedMotion);
    };
    
    loadSettings();
  }, []);
  
  // Save animation settings
  const handleSaveSettings = async () => {
    setIsSaving(true);
    
    try {
      await saveAnimationSettings({
        enabled: animationsEnabled,
        transitionType: transitionType,
        duration: DEFAULT_ANIMATION_SETTINGS.duration,
        reducedMotion: respectReducedMotion
      });
      
      setSaveStatus({
        show: true,
        message: 'Animation settings saved successfully'
      });
      
      // Hide the success message after 3 seconds
      setTimeout(() => {
        setSaveStatus({ show: false, message: '' });
      }, 3000);
    } catch (error) {
      console.error('Error saving animation settings:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Effect to save settings when transition type changes
  useEffect(() => {
    if (transitionType) {
      handleSaveSettings();
    }
  }, [transitionType]);
  
  // Routes for the route transition demo
  const routes: Record<string, React.ReactNode> = {
    'home': (
      <div className="p-6 bg-notely-card rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4">Home Route</h2>
        <p>This simulates navigating between different routes in your application.</p>
        <p className="mt-2">Click the navigation buttons to see the route transitions in action.</p>
      </div>
    ),
    'about': (
      <div className="p-6 bg-notely-card rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4">About Route</h2>
        <p>This is the about page with smooth transition animations.</p>
        <p className="mt-2">Route transitions make your application feel more polished and responsive.</p>
      </div>
    ),
    'contact': (
      <div className="p-6 bg-notely-card rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4">Contact Route</h2>
        <p>This is the contact page with the same smooth transitions.</p>
        <p className="mt-2">Consistent animations across your application create a cohesive user experience.</p>
      </div>
    )
  };
  
  return (
    <div className="p-6 notely-card rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold mb-4">Animation Settings</h2>
      <p className="text-sm mb-6">Preview and configure animation transitions for your extension.</p>
      
      {/* Animation Toggle Controls */}
      <div className="mb-6 space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-md font-medium">Enable Animations</h3>
            <p className="text-sm text-notely-text-secondary">Turn animations on or off throughout the extension</p>
          </div>
          <Toggle
            enabled={animationsEnabled}
            onChange={(enabled) => {
              setAnimationsEnabled(enabled);
              handleSaveSettings();
            }}
          />
        </div>
        
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-md font-medium">Respect Reduced Motion</h3>
            <p className="text-sm text-notely-text-secondary">Use simpler animations for users who prefer reduced motion</p>
          </div>
          <Toggle
            enabled={respectReducedMotion}
            onChange={(enabled) => {
              setRespectReducedMotion(enabled);
              handleSaveSettings();
            }}
          />
        </div>
        
        {saveStatus.show && (
          <div className="mt-2 p-2 bg-green-100 text-green-800 rounded-md text-sm">
            {saveStatus.message}
          </div>
        )}
      </div>
      
      {/* Transition Type Selector */}
      <div className="mb-6">
        <h3 className="text-md font-medium mb-3">Transition Type</h3>
        <div className="flex flex-wrap gap-2">
          <AnimatedButton 
            variant={transitionType === 'fade' ? 'default' : 'outline'}
            onClick={() => setTransitionType('fade')}
            size="sm"
          >
            Fade
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'slide' ? 'default' : 'outline'}
            onClick={() => setTransitionType('slide')}
            size="sm"
          >
            Slide
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'scale' ? 'default' : 'outline'}
            onClick={() => setTransitionType('scale')}
            size="sm"
          >
            Scale
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'none' ? 'default' : 'outline'}
            onClick={() => setTransitionType('none')}
            size="sm"
          >
            None
          </AnimatedButton>
        </div>
      </div>
      
      {/* Page Transition Demo */}
      <AnimatedCard className="mb-6">
        <CardHeader>
          <CardTitle>Page Transition Demo</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="flex flex-wrap gap-2 mb-4">
              <AnimatedButton 
                variant={currentPage === 1 ? 'default' : 'outline'}
                onClick={() => setCurrentPage(1)}
                size="sm"
              >
                Page 1
              </AnimatedButton>
              <AnimatedButton 
                variant={currentPage === 2 ? 'default' : 'outline'}
                onClick={() => setCurrentPage(2)}
                size="sm"
              >
                Page 2
              </AnimatedButton>
              <AnimatedButton 
                variant={currentPage === 3 ? 'default' : 'outline'}
                onClick={() => setCurrentPage(3)}
                size="sm"
              >
                Page 3
              </AnimatedButton>
            </div>
            
            <div className="min-h-[200px]">
              <PageTransition
                key={currentPage}
                type={transitionType}
              >
                {currentPage === 1 && <Page1 />}
                {currentPage === 2 && <Page2 />}
                {currentPage === 3 && <Page3 />}
              </PageTransition>
            </div>
          </div>
        </CardContent>
      </AnimatedCard>
      
      {/* Route Transition Demo */}
      <AnimatedCard>
        <CardHeader>
          <CardTitle>Route Transition Demo</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="flex flex-wrap gap-2 mb-4">
              <AnimatedButton 
                variant={currentRoute === 'home' ? 'default' : 'outline'}
                onClick={() => setCurrentRoute('home')}
                size="sm"
              >
                Home
              </AnimatedButton>
              <AnimatedButton 
                variant={currentRoute === 'about' ? 'default' : 'outline'}
                onClick={() => setCurrentRoute('about')}
                size="sm"
              >
                About
              </AnimatedButton>
              <AnimatedButton 
                variant={currentRoute === 'contact' ? 'default' : 'outline'}
                onClick={() => setCurrentRoute('contact')}
                size="sm"
              >
                Contact
              </AnimatedButton>
            </div>
            
            <div className="min-h-[200px]">
              <RouteTransition
                routeKey={currentRoute}
                transitionType={transitionType}
              >
                {routes[currentRoute]}
              </RouteTransition>
            </div>
          </div>
        </CardContent>
      </AnimatedCard>
    </div>
  );
};

export default AnimationSettings;