import React, { useState } from 'react';
import { RouteSimulator, RouteTransition } from './ui/RouteTransition';
import { AnimatedButton } from './ui/AnimatedButton';
import { useViewTransition } from '../hooks/useRouteTransition';
import { TransitionType } from './ui/PageTransition';

// Sample route content components
const HomePage = () => (
  <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">Home Page</h2>
    <p className="mb-4">Welcome to the home page! This simulates a route transition.</p>
    <p>Notice how the content animates smoothly when navigating between routes.</p>
  </div>
);

const AboutPage = () => (
  <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">About Page</h2>
    <p className="mb-4">This is the about page with information about our company.</p>
    <p>The route transition makes navigation feel more natural and engaging.</p>
  </div>
);

const ContactPage = () => (
  <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">Contact Page</h2>
    <p className="mb-4">Get in touch with us using the contact information below.</p>
    <p>Route transitions improve the perceived performance of navigation.</p>
  </div>
);

const ProductsPage = () => (
  <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">Products Page</h2>
    <p className="mb-4">Browse our selection of products.</p>
    <p>Each page transition can use different animation types for different effects.</p>
  </div>
);

/**
 * Demo component for showcasing route transitions
 */
const RouteTransitionDemo: React.FC = () => {
  // State for transition type
  const [transitionType, setTransitionType] = useState<TransitionType>('fade');
  
  // Sample routes for the RouteSimulator
  const routes = {
    'Home': <HomePage />,
    'About': <AboutPage />,
    'Contact': <ContactPage />,
    'Products': <ProductsPage />
  };
  
  // Use the view transition hook for the manual example
  const {
    activeView,
    changeView,
    isTransitioning
  } = useViewTransition('Home', {
    transitionType,
    exitDuration: 300,
    onBeforeNavigate: () => console.log('Before navigation'),
    onAfterNavigate: () => console.log('After navigation')
  });
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Route Transition Demo</h1>
      
      {/* Transition type selector */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Transition Type</h2>
        <div className="flex flex-wrap gap-2">
          <AnimatedButton 
            variant={transitionType === 'fade' ? 'default' : 'outline'}
            onClick={() => setTransitionType('fade')}
          >
            Fade
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'slide' ? 'default' : 'outline'}
            onClick={() => setTransitionType('slide')}
          >
            Slide
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'scale' ? 'default' : 'outline'}
            onClick={() => setTransitionType('scale')}
          >
            Scale
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'none' ? 'default' : 'outline'}
            onClick={() => setTransitionType('none')}
          >
            None
          </AnimatedButton>
        </div>
      </div>
      
      {/* RouteSimulator Demo */}
      <section className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Route Simulator</h2>
        <p className="mb-4">This component simulates route changes with animations:</p>
        
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <RouteSimulator
            routes={routes}
            initialRoute="Home"
            transitionType={transitionType}
          />
        </div>
      </section>
      
      {/* Manual Route Transition Demo */}
      <section className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Manual Route Transition with Hook</h2>
        <p className="mb-4">This example uses the useViewTransition hook:</p>
        
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          {/* Navigation buttons */}
          <div className="flex flex-wrap gap-2 mb-4">
            {Object.keys(routes).map((route) => (
              <AnimatedButton
                key={route}
                variant={activeView === route ? 'default' : 'outline'}
                onClick={() => changeView(route)}
                disabled={isTransitioning}
              >
                {route}
              </AnimatedButton>
            ))}
          </div>
          
          {/* Route content with transition */}
          <RouteTransition
            routeKey={activeView}
            transitionType={transitionType}
          >
            {routes[activeView as keyof typeof routes]}
          </RouteTransition>
        </div>
      </section>
      
      {/* Implementation Examples */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Implementation Examples</h2>
        <div className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg">
          <pre className="text-sm overflow-x-auto">
            {`
// Basic usage with React Router
import { useLocation } from 'react-router-dom';

const App = () => {
  const location = useLocation();
  
  return (
    <RouteTransitionProvider
      location={location.pathname}
      transitionType="fade"
    >
      <Routes location={location}>
        <Route path="/" element={<HomePage />} />
        <Route path="/about" element={<AboutPage />} />
        <Route path="/contact" element={<ContactPage />} />
      </Routes>
    </RouteTransitionProvider>
  );
};

// Using the useRouteTransition hook
import { useRouteTransition } from '../hooks/useRouteTransition';

const MyComponent = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const { 
    routeKey, 
    navigateTo, 
    transitionType, 
    setTransitionType 
  } = useRouteTransition(
    location.pathname,
    navigate,
    { 
      transitionType: 'slide',
      exitDuration: 300
    }
  );
  
  return (
    <div>
      <button onClick={() => navigateTo('/about')}>Go to About</button>
      <button onClick={() => setTransitionType('scale')}>Use Scale Animation</button>
      
      <RouteTransition
        routeKey={routeKey}
        transitionType={transitionType}
      >
        <Outlet />
      </RouteTransition>
    </div>
  );
};
            `}
          </pre>
        </div>
      </section>
    </div>
  );
};

export default RouteTransitionDemo;