import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';

interface SlidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  position?: 'left' | 'right' | 'top' | 'bottom';
  size?: 'sm' | 'md' | 'lg' | 'full';
  className?: string;
  overlayClassName?: string;
  closeOnOverlayClick?: boolean;
  showCloseButton?: boolean;
}

/**
 * SlidePanel component for sliding panels from any edge of the screen
 */
export const SlidePanel: React.FC<SlidePanelProps> = ({
  isOpen,
  onClose,
  title,
  children,
  position = 'right',
  size = 'md',
  className = '',
  overlayClassName = '',
  closeOnOverlayClick = true,
  showCloseButton = true
}) => {
  // Size classes
  const sizeClasses = {
    sm: {
      left: 'w-64',
      right: 'w-64',
      top: 'h-64',
      bottom: 'h-64'
    },
    md: {
      left: 'w-80',
      right: 'w-80',
      top: 'h-80',
      bottom: 'h-80'
    },
    lg: {
      left: 'w-96',
      right: 'w-96',
      top: 'h-96',
      bottom: 'h-96'
    },
    full: {
      left: 'w-full',
      right: 'w-full',
      top: 'h-full',
      bottom: 'h-full'
    }
  };
  
  // Position variants
  const panelVariants = {
    left: {
      hidden: { x: '-100%', opacity: 0 },
      visible: { x: 0, opacity: 1 },
      exit: { x: '-100%', opacity: 0 }
    },
    right: {
      hidden: { x: '100%', opacity: 0 },
      visible: { x: 0, opacity: 1 },
      exit: { x: '100%', opacity: 0 }
    },
    top: {
      hidden: { y: '-100%', opacity: 0 },
      visible: { y: 0, opacity: 1 },
      exit: { y: '-100%', opacity: 0 }
    },
    bottom: {
      hidden: { y: '100%', opacity: 0 },
      visible: { y: 0, opacity: 1 },
      exit: { y: '100%', opacity: 0 }
    }
  };
  
  // Overlay variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };
  
  // Position classes
  const positionClasses = {
    left: 'top-0 left-0 h-full',
    right: 'top-0 right-0 h-full',
    top: 'top-0 left-0 w-full',
    bottom: 'bottom-0 left-0 w-full'
  };
  
  // Lock body scroll when panel is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);
  
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);
  
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop overlay */}
          <motion.div
            className={cn(
              'fixed inset-0 bg-black/50 z-40',
              overlayClassName
            )}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={overlayVariants}
            transition={{ duration: 0.2 }}
            onClick={closeOnOverlayClick ? onClose : undefined}
          />
          
          {/* Panel */}
          <motion.div
            className={cn(
              'fixed z-50 bg-notely-card shadow-lg flex flex-col',
              positionClasses[position],
              sizeClasses[size][position],
              className
            )}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={panelVariants[position]}
            transition={{ 
              type: 'spring', 
              damping: 30, 
              stiffness: 300 
            }}
          >
            {/* Header */}
            {(title || showCloseButton) && (
              <div className="flex items-center justify-between p-4 border-b border-notely-border">
                {title && (
                  <h2 className="text-lg font-semibold text-notely-text-primary">{title}</h2>
                )}
                
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className="p-1 rounded-full hover:bg-notely-surface focus:outline-none focus:ring-2 focus:ring-notely-accent"
                    aria-label="Close panel"
                  >
                    <svg className="w-5 h-5 text-notely-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            )}
            
            {/* Content */}
            <div className="flex-1 overflow-auto">
              {children}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

interface CollapsiblePanelProps {
  isOpen: boolean;
  onToggle: () => void;
  title: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  icon?: React.ReactNode;
}

/**
 * CollapsiblePanel component for expandable/collapsible content sections
 */
export const CollapsiblePanel: React.FC<CollapsiblePanelProps> = ({
  isOpen,
  onToggle,
  title,
  children,
  className = '',
  headerClassName = '',
  contentClassName = '',
  icon
}) => {
  // Animation variants
  const contentVariants = {
    hidden: { height: 0, opacity: 0 },
    visible: { 
      height: 'auto', 
      opacity: 1,
      transition: { 
        height: { type: 'spring', stiffness: 300, damping: 30 },
        opacity: { duration: 0.2 }
      }
    },
    exit: { 
      height: 0, 
      opacity: 0,
      transition: { 
        height: { duration: 0.2 },
        opacity: { duration: 0.1 }
      }
    }
  };
  
  // Icon rotation variants
  const iconVariants = {
    open: { rotate: 180 },
    closed: { rotate: 0 }
  };
  
  return (
    <div className={cn('border border-notely-border rounded-lg overflow-hidden', className)}>
      {/* Header */}
      <button
        className={cn(
          'w-full flex items-center justify-between p-4 text-left focus:outline-none focus:ring-2 focus:ring-notely-accent focus:ring-inset',
          isOpen ? 'bg-notely-surface' : 'bg-notely-card hover:bg-notely-surface',
          headerClassName
        )}
        onClick={onToggle}
        aria-expanded={isOpen}
      >
        <div className="flex items-center">
          {icon && <span className="mr-3">{icon}</span>}
          <span className="font-medium text-notely-text-primary">{title}</span>
        </div>
        
        <motion.div
          animate={isOpen ? 'open' : 'closed'}
          variants={iconVariants}
          transition={{ duration: 0.2 }}
        >
          <svg className="w-5 h-5 text-notely-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </motion.div>
      </button>
      
      {/* Content */}
      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={contentVariants}
            className="overflow-hidden"
          >
            <div className={cn('p-4 border-t border-notely-border', contentClassName)}>
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};