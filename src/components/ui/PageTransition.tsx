import React, { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AnimationVariants, getAnimationVariant, prefersReducedMotion } from '../../utils/animationUtils';

// Default animation variants for page transitions
export const pageTransitionVariants: AnimationVariants = {
  initial: { opacity: 0, y: 10 },
  animate: { 
    opacity: 1, 
    y: 0, 
    transition: { 
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1] // Material Design standard easing
    } 
  },
  exit: { 
    opacity: 0, 
    y: -10, 
    transition: { 
      duration: 0.2,
      ease: [0.4, 0.0, 1, 1] // Accelerate easing for exit
    } 
  }
};

// Slide animation variants
export const slideTransitionVariants: AnimationVariants = {
  initial: { opacity: 0, x: 20 },
  animate: { 
    opacity: 1, 
    x: 0, 
    transition: { 
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1]
    } 
  },
  exit: { 
    opacity: 0, 
    x: -20, 
    transition: { 
      duration: 0.2,
      ease: [0.4, 0.0, 1, 1]
    } 
  }
};

// Fade animation variants
export const fadeTransitionVariants: AnimationVariants = {
  initial: { opacity: 0 },
  animate: { 
    opacity: 1, 
    transition: { 
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1]
    } 
  },
  exit: { 
    opacity: 0, 
    transition: { 
      duration: 0.2,
      ease: [0.4, 0.0, 1, 1]
    } 
  }
};

// Scale animation variants
export const scaleTransitionVariants: AnimationVariants = {
  initial: { opacity: 0, scale: 0.97 },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: { 
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1]
    } 
  },
  exit: { 
    opacity: 0, 
    scale: 1.03,
    transition: { 
      duration: 0.2,
      ease: [0.4, 0.0, 1, 1]
    } 
  }
};

export type TransitionType = 'fade' | 'slide' | 'scale' | 'none';

interface PageTransitionProps {
  children: ReactNode;
  type?: TransitionType;
  customVariants?: AnimationVariants;
  className?: string;
  isPresent?: boolean;
  pageKey?: string | number;
  onAnimationComplete?: () => void;
  onExitComplete?: () => void;
}

/**
 * PageTransition component for smooth transitions between pages or views
 * 
 * @param children - The content to be rendered with transitions
 * @param type - The type of transition animation (fade, slide, scale, none)
 * @param customVariants - Custom animation variants if needed
 * @param className - Additional CSS classes
 * @param isPresent - Whether the component should be visible (defaults to true)
 * @param pageKey - Unique key for the page to ensure proper animation
 * @param onAnimationComplete - Callback when animation completes
 * @param onExitComplete - Callback when exit animation completes
 */
export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  type = 'fade',
  customVariants,
  className = '',
  isPresent = true,
  pageKey,
  onAnimationComplete,
  onExitComplete
}) => {
  // Select the appropriate animation variant based on type
  let variants: AnimationVariants;
  
  switch (type) {
    case 'slide':
      variants = slideTransitionVariants;
      break;
    case 'scale':
      variants = scaleTransitionVariants;
      break;
    case 'fade':
      variants = fadeTransitionVariants;
      break;
    case 'none':
      variants = {
        initial: {},
        animate: {},
        exit: {}
      };
      break;
    default:
      variants = pageTransitionVariants;
  }

  // Use custom variants if provided
  const animationVariant = getAnimationVariant(customVariants || variants);
  
  // If reduced motion is preferred, use simpler animations
  const shouldReduceMotion = prefersReducedMotion();

  // Get CSS class based on transition type - using stronger CSS animations
  const getTransitionClass = () => {
    if (type === 'none') return '';
    
    if (type === 'slide') {
      return 'animate-slideInRight';
    } else if (type === 'scale') {
      return 'animate-scaleIn';
    } else {
      // Default to fade
      return 'animate-fadeIn';
    }
  };

  return (
    <AnimatePresence mode="wait" onExitComplete={onExitComplete}>
      {isPresent && (
        <div 
          className={`page-transition-wrapper ${getTransitionClass()} ${className}`}
          style={{ animation: `${getTransitionClass()} 0.5s forwards` }}
        >
          {children}
        </div>
      )}
    </AnimatePresence>
  );
};

/**
 * A simpler version of PageTransition that doesn't require a key
 * Useful for content that doesn't need to be keyed for transitions
 */
export const ContentTransition: React.FC<Omit<PageTransitionProps, 'pageKey'>> = (props) => {
  return <PageTransition {...props} pageKey="content" />;
};

/**
 * A component that transitions between different content based on the active view
 */
interface ViewTransitionProps {
  activeView: string;
  views: Record<string, ReactNode>;
  transitionType?: TransitionType;
  className?: string;
}

export const ViewTransition: React.FC<ViewTransitionProps> = ({
  activeView,
  views,
  transitionType = 'fade',
  className = ''
}) => {
  return (
    <AnimatePresence mode="wait">
      <PageTransition
        key={activeView}
        type={transitionType}
        className={className}
      >
        {views[activeView]}
      </PageTransition>
    </AnimatePresence>
  );
};