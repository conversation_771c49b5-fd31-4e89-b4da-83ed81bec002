import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface ProgressProps {
  value: number;
  max?: number;
  label?: string;
  showValue?: boolean;
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  valueClassName?: string;
  indeterminate?: boolean;
}

/**
 * Progress component for displaying linear progress
 */
export const Progress: React.FC<ProgressProps> = ({
  value,
  max = 100,
  label,
  showValue = false,
  color = 'default',
  size = 'md',
  className = '',
  valueClassName = '',
  indeterminate = false
}) => {
  // Calculate percentage
  const percentage = Math.min(100, Math.max(0, (value / max) * 100));
  
  // Size classes
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };
  
  // Color classes
  const colorClasses = {
    default: 'bg-notely-accent',
    primary: 'bg-blue-500',
    secondary: 'bg-purple-500',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500'
  };
  
  return (
    <div className="w-full">
      {label && (
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm text-notely-text-secondary">{label}</span>
          {showValue && (
            <span className={cn("text-sm font-medium", valueClassName)}>
              {Math.round(percentage)}%
            </span>
          )}
        </div>
      )}
      
      <div className={cn("w-full bg-notely-surface rounded-full overflow-hidden", sizeClasses[size], className)}>
        {indeterminate ? (
          <motion.div
            className={cn("h-full rounded-full", colorClasses[color])}
            initial={{ width: "0%" }}
            animate={{ 
              width: ["0%", "100%", "0%"],
              x: ["-100%", "100%", "-100%"]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        ) : (
          <motion.div
            className={cn("h-full rounded-full", colorClasses[color])}
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        )}
      </div>
    </div>
  );
};

interface CircularProgressProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  thickness?: number;
  showValue?: boolean;
  className?: string;
  valueClassName?: string;
  indeterminate?: boolean;
}

/**
 * CircularProgress component for displaying circular progress
 */
export const CircularProgress: React.FC<CircularProgressProps> = ({
  value,
  max = 100,
  size = 'md',
  color = 'default',
  thickness = 4,
  showValue = false,
  className = '',
  valueClassName = '',
  indeterminate = false
}) => {
  // Calculate percentage and circle properties
  const percentage = Math.min(100, Math.max(0, (value / max) * 100));
  
  // Size in pixels
  const sizeInPixels = {
    sm: 32,
    md: 48,
    lg: 64
  }[size];
  
  const radius = (sizeInPixels / 2) - (thickness / 2);
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;
  
  // Color classes
  const colorClasses = {
    default: 'stroke-notely-accent',
    primary: 'stroke-blue-500',
    secondary: 'stroke-purple-500',
    success: 'stroke-green-500',
    warning: 'stroke-yellow-500',
    error: 'stroke-red-500'
  };
  
  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        className="transform -rotate-90"
        width={sizeInPixels}
        height={sizeInPixels}
        viewBox={`0 0 ${sizeInPixels} ${sizeInPixels}`}
      >
        {/* Background circle */}
        <circle
          cx={sizeInPixels / 2}
          cy={sizeInPixels / 2}
          r={radius}
          fill="none"
          strokeWidth={thickness}
          className="stroke-notely-surface"
        />
        
        {/* Progress circle */}
        {indeterminate ? (
          <motion.circle
            cx={sizeInPixels / 2}
            cy={sizeInPixels / 2}
            r={radius}
            fill="none"
            strokeWidth={thickness}
            className={colorClasses[color]}
            strokeDasharray={circumference}
            initial={{ strokeDashoffset: circumference }}
            animate={{ 
              strokeDashoffset: [circumference, 0, circumference],
              rotate: [0, 360]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        ) : (
          <motion.circle
            cx={sizeInPixels / 2}
            cy={sizeInPixels / 2}
            r={radius}
            fill="none"
            strokeWidth={thickness}
            className={colorClasses[color]}
            strokeDasharray={circumference}
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        )}
      </svg>
      
      {/* Value display */}
      {showValue && !indeterminate && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className={cn("text-xs font-medium", valueClassName)}>
            {Math.round(percentage)}%
          </span>
        </div>
      )}
    </div>
  );
};