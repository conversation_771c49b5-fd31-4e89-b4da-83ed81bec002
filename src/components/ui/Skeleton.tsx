import React from 'react';
import { cn } from '../../lib/utils';
import { motion } from 'framer-motion';

interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
  animation?: 'pulse' | 'wave' | 'none';
}

/**
 * Skeleton component for loading states
 */
export const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  variant = 'rectangular',
  animation = 'pulse'
}) => {
  const baseClasses = 'bg-notely-surface/80';
  
  const variantClasses = {
    text: 'h-4 w-full rounded',
    rectangular: 'rounded-md',
    circular: 'rounded-full'
  };
  
  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'skeleton-wave',
    none: ''
  };
  
  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
    />
  );
};

interface SkeletonTextProps {
  lines?: number;
  className?: string;
  lineClassName?: string;
  animation?: 'pulse' | 'wave' | 'none';
}

/**
 * SkeletonText component for loading text content
 */
export const SkeletonText: React.FC<SkeletonTextProps> = ({
  lines = 3,
  className = '',
  lineClassName = '',
  animation = 'pulse'
}) => {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          className={cn(
            index === lines - 1 ? 'w-4/5' : 'w-full',
            lineClassName
          )}
          animation={animation}
        />
      ))}
    </div>
  );
};

/**
 * SkeletonCard component for loading card content
 */
export const SkeletonCard: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={cn('rounded-lg border border-notely-border overflow-hidden', className)}>
      <Skeleton className="h-48 w-full" />
      <div className="p-4 space-y-3">
        <Skeleton variant="text" className="h-6 w-2/3" />
        <SkeletonText lines={2} />
        <div className="flex justify-between pt-2">
          <Skeleton variant="text" className="h-4 w-20" />
          <Skeleton variant="text" className="h-4 w-16" />
        </div>
      </div>
    </div>
  );
};

interface SkeletonListProps {
  count?: number;
  className?: string;
  itemClassName?: string;
}

/**
 * SkeletonList component for loading list content
 */
export const SkeletonList: React.FC<SkeletonListProps> = ({
  count = 3,
  className = '',
  itemClassName = ''
}) => {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={cn('flex items-center space-x-3', itemClassName)}>
          <Skeleton variant="circular" className="h-10 w-10" />
          <div className="space-y-2 flex-1">
            <Skeleton variant="text" className="h-4 w-3/4" />
            <Skeleton variant="text" className="h-3 w-1/2" />
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Animated skeleton component with wave effect
 */
export const AnimatedSkeleton: React.FC<SkeletonProps> = ({
  className = '',
  variant = 'rectangular'
}) => {
  const baseClasses = 'relative overflow-hidden bg-notely-surface/80';
  
  const variantClasses = {
    text: 'h-4 w-full rounded',
    rectangular: 'rounded-md',
    circular: 'rounded-full'
  };
  
  return (
    <div className={cn(baseClasses, variantClasses[variant], className)}>
      <motion.div
        className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-notely-surface/30 to-transparent"
        animate={{ x: ['0%', '100%'] }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </div>
  );
};