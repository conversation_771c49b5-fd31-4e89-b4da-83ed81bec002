import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';
import { prefersReducedMotion } from '../../utils/animationUtils';

interface AnimatedListProps<T> {
  items: T[];
  keyExtractor: (item: T) => string | number;
  renderItem: (item: T, index: number) => React.ReactNode;
  className?: string;
  itemClassName?: string;
  staggerDelay?: number;
  role?: string;
}

/**
 * AnimatedList component that animates items when they enter, exit, or move
 */
export function AnimatedList<T>({
  items,
  keyExtractor,
  renderItem,
  className = '',
  itemClassName = '',
  staggerDelay = 0.05,
  role = 'list'
}: AnimatedListProps<T>) {
  const reducedMotion = prefersReducedMotion();
  
  // Animation variants
  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: reducedMotion ? 0 : staggerDelay
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: reducedMotion ? 0 : 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: reducedMotion ? 0.1 : 0.2,
        ease: [0.4, 0.0, 0.2, 1]
      }
    },
    exit: { 
      opacity: 0, 
      y: reducedMotion ? 0 : -10,
      transition: { 
        duration: reducedMotion ? 0.1 : 0.15,
        ease: [0.4, 0.0, 1, 1]
      }
    }
  };
  
  return (
    <motion.ul
      className={className}
      role={role}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <AnimatePresence initial={false}>
        {items.map((item, index) => (
          <motion.li
            key={keyExtractor(item)}
            variants={itemVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            layout
            className={itemClassName}
          >
            {renderItem(item, index)}
          </motion.li>
        ))}
      </AnimatePresence>
    </motion.ul>
  );
}

interface AnimatedGridProps<T> {
  items: T[];
  keyExtractor: (item: T) => string | number;
  renderItem: (item: T, index: number) => React.ReactNode;
  columns?: { sm?: number; md?: number; lg?: number; };
  gap?: string;
  className?: string;
  itemClassName?: string;
  staggerDelay?: number;
}

/**
 * AnimatedGrid component that animates grid items when they enter, exit, or move
 */
export function AnimatedGrid<T>({
  items,
  keyExtractor,
  renderItem,
  columns = { sm: 2, md: 3, lg: 4 },
  gap = 'gap-4',
  className = '',
  itemClassName = '',
  staggerDelay = 0.05
}: AnimatedGridProps<T>) {
  const reducedMotion = prefersReducedMotion();
  
  // Generate responsive grid classes
  const gridCols = [
    `grid-cols-1`,
    columns.sm ? `sm:grid-cols-${columns.sm}` : '',
    columns.md ? `md:grid-cols-${columns.md}` : '',
    columns.lg ? `lg:grid-cols-${columns.lg}` : ''
  ].filter(Boolean).join(' ');
  
  // Animation variants
  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: reducedMotion ? 0 : staggerDelay
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, scale: reducedMotion ? 1 : 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: reducedMotion ? 0.1 : 0.3,
        ease: [0.4, 0.0, 0.2, 1]
      }
    },
    exit: { 
      opacity: 0, 
      scale: reducedMotion ? 1 : 0.95,
      transition: { 
        duration: reducedMotion ? 0.1 : 0.2,
        ease: [0.4, 0.0, 1, 1]
      }
    }
  };
  
  return (
    <motion.div
      className={cn(`grid ${gridCols} ${gap}`, className)}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <AnimatePresence initial={false}>
        {items.map((item, index) => (
          <motion.div
            key={keyExtractor(item)}
            variants={itemVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            layout
            className={itemClassName}
          >
            {renderItem(item, index)}
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
}