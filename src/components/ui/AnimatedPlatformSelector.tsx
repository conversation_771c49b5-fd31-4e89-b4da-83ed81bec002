import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AnimatedTabs } from './AnimatedTabs';
import { PlatformLogo } from '../PlatformLogo';
import { Platform } from '../../types';

type ViewMode = Platform | 'All' | 'Mindstream';

interface AnimatedPlatformSelectorProps {
  onSelect: (platform: ViewMode) => void;
  selectedPlatform: ViewMode;
  onCategoryViewToggle?: (platform: ViewMode) => void;
  enabledPlatforms?: Record<string, boolean>;
}

/**
 * AnimatedPlatformSelector component for platform selection with animations
 */
export const AnimatedPlatformSelector: React.FC<AnimatedPlatformSelectorProps> = ({
  onSelect,
  selectedPlatform,
  onCategoryViewToggle,
  enabledPlatforms = {}
}) => {
  // Ensure these values exactly match the Platform type in types.ts and background.ts
  const allPlatforms: ViewMode[] = ['Mindstream', 'All', 'X/Twitter', 'LinkedIn', 'Reddit', 'Instagram', 'pinterest', 'Web'];
  
  // Filter platforms based on enabled integrations
  const platforms = allPlatforms.filter(platform => {
    // Always show Mindstream and All tabs
    if (platform === 'Mindstream' || platform === 'All') return true;
    
    // For other platforms, check if they're enabled in settings
    // If enabledPlatforms is empty (not loaded yet), show all platforms
    return Object.keys(enabledPlatforms).length === 0 || enabledPlatforms[platform] === true;
  });

  // Convert platforms to tabs format
  const tabs = platforms.map(platform => {
    // Handle logo element for different platform types
    let logoElement = null;
    if (platform === 'Mindstream') {
      logoElement = <span className="text-lg">🧠</span>;
    } else if (platform !== 'All') {
      logoElement = <PlatformLogo platform={platform as Platform} className="w-4 h-4" />;
    }

    // Format the label with logo
    const label = (
      <div className="flex items-center">
        {logoElement && <span className={`mr-1.5 ${platform === 'Mindstream' ? 'text-lg' : ''}`}>{logoElement}</span>}
        <span>{platform === 'pinterest' ? 'Pinterest' : platform === 'All' ? 'All' : platform === 'Mindstream' ? 'Mindstream' : platform}</span>
      </div>
    );

    return {
      id: platform,
      label,
      content: <></>, // Content will be rendered separately
      disabled: false
    };
  });

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    const platform = tabId as ViewMode;
    onSelect(platform);
    
    // Trigger category view for specific platforms
    if (platform !== 'All' && platform !== 'Mindstream' && onCategoryViewToggle) {
      onCategoryViewToggle(platform);
    }
  };

  return (
    <div className="w-full">
      <AnimatedTabs
        tabs={tabs}
        defaultTabId={selectedPlatform}
        variant="pills"
        className="platform-tabs"
        tabsClassName="platform-tabs-header"
        onChange={handleTabChange}
      />
    </div>
  );
};

export default AnimatedPlatformSelector;