import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'gray' | 'white';
  className?: string;
}

/**
 * LoadingSpinner component for displaying loading states with animations
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  // Size classes
  const sizeClasses = {
    xs: 'h-3 w-3 border',
    sm: 'h-4 w-4 border',
    md: 'h-6 w-6 border-2',
    lg: 'h-8 w-8 border-2',
    xl: 'h-10 w-10 border-3'
  };
  
  // Color classes
  const colorClasses = {
    primary: 'border-notely-accent border-t-transparent',
    secondary: 'border-purple-500 border-t-transparent',
    gray: 'border-gray-300 border-t-transparent',
    white: 'border-white border-t-transparent'
  };
  
  return (
    <motion.div
      className={cn(
        'rounded-full',
        sizeClasses[size],
        colorClasses[color],
        className
      )}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    />
  );
};

interface LoadingDotsProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'gray' | 'white';
  className?: string;
}

/**
 * LoadingDots component for displaying loading states with dot animations
 */
export const LoadingDots: React.FC<LoadingDotsProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  // Size classes
  const sizeClasses = {
    sm: 'h-1.5 w-1.5',
    md: 'h-2 w-2',
    lg: 'h-2.5 w-2.5'
  };
  
  // Color classes
  const colorClasses = {
    primary: 'bg-notely-accent',
    secondary: 'bg-purple-500',
    gray: 'bg-gray-300',
    white: 'bg-white'
  };
  
  // Animation variants
  const containerVariants = {
    animate: {
      transition: {
        staggerChildren: 0.2
      }
    }
  };
  
  const dotVariants = {
    initial: { y: 0 },
    animate: {
      y: [0, -4, 0],
      transition: {
        duration: 0.6,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };
  
  return (
    <motion.div
      className={cn('flex space-x-1', className)}
      variants={containerVariants}
      initial="initial"
      animate="animate"
    >
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className={cn(
            'rounded-full',
            sizeClasses[size],
            colorClasses[color]
          )}
          variants={dotVariants}
        />
      ))}
    </motion.div>
  );
};