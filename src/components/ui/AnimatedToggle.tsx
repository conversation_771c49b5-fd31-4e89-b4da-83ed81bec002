import React from "react";
import { motion } from "framer-motion";
import { cn } from "../../lib/utils";
import { toggleTransitionClass } from "../../styles/animation-classes";
import { prefersReducedMotion } from "../../utils/animationUtils";

interface ToggleProps {
  isOn: boolean;
  onToggle: () => void;
  label?: string;
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  className?: string;
  activeColor?: string;
  inactiveColor?: string;
  thumbColor?: string;
  ariaLabel?: string;
}

/**
 * Animated toggle switch component with smooth transitions
 */
export const AnimatedToggle = ({
  isOn,
  onToggle,
  label,
  size = "md",
  disabled = false,
  className,
  activeColor = "bg-primary",
  inactiveColor = "bg-gray-300 dark:bg-gray-600",
  thumbColor = "bg-white",
  ariaLabel
}: ToggleProps) => {
  // Determine if reduced motion is preferred
  const reducedMotion = prefersReducedMotion();
  
  // Size mappings
  const sizeClasses = {
    sm: {
      track: "w-8 h-4",
      thumb: "w-3 h-3",
      thumbOffset: 16,
      label: "text-sm"
    },
    md: {
      track: "w-11 h-6",
      thumb: "w-5 h-5",
      thumbOffset: 24,
      label: "text-base"
    },
    lg: {
      track: "w-14 h-7",
      thumb: "w-6 h-6",
      thumbOffset: 32,
      label: "text-lg"
    }
  };
  
  // Get the appropriate size configuration
  const sizeConfig = sizeClasses[size];
  
  // Animation spring configuration
  const spring = {
    type: "spring",
    stiffness: 500,
    damping: 30,
    duration: reducedMotion ? 0.1 : undefined
  };
  
  return (
    <div className={cn("flex items-center", className)}>
      <button
        type="button"
        role="switch"
        aria-checked={isOn}
        aria-label={ariaLabel || label}
        disabled={disabled}
        onClick={onToggle}
        className={cn(
          "relative inline-flex flex-shrink-0 rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",
          sizeConfig.track,
          isOn ? activeColor : inactiveColor,
          disabled && "opacity-50 cursor-not-allowed"
        )}
      >
        <span className="sr-only">{label || "Toggle"}</span>
        <motion.span
          className={cn(
            "inline-block rounded-full shadow transform ring-0",
            sizeConfig.thumb,
            thumbColor
          )}
          initial={false}
          animate={{
            x: isOn ? sizeConfig.thumbOffset : 1
          }}
          transition={spring}
        />
      </button>
      {label && (
        <span 
          className={cn(
            "ml-2", 
            sizeConfig.label,
            disabled ? "text-gray-400 dark:text-gray-600" : "text-gray-700 dark:text-gray-300"
          )}
        >
          {label}
        </span>
      )}
    </div>
  );
};

interface ToggleGroupProps {
  options: { value: string; label: string }[];
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
  orientation?: "horizontal" | "vertical";
}

/**
 * Animated toggle group component for selecting from multiple options
 */
export const AnimatedToggleGroup = ({
  options,
  value,
  onChange,
  disabled = false,
  className,
  orientation = "horizontal"
}: ToggleGroupProps) => {
  // Determine if reduced motion is preferred
  const reducedMotion = prefersReducedMotion();
  
  return (
    <div 
      className={cn(
        "inline-flex rounded-md p-1 bg-gray-100 dark:bg-gray-800",
        orientation === "horizontal" ? "flex-row" : "flex-col",
        className
      )}
      role="radiogroup"
    >
      {options.map((option) => {
        const isActive = option.value === value;
        
        return (
          <button
            key={option.value}
            type="button"
            role="radio"
            aria-checked={isActive}
            disabled={disabled}
            onClick={() => onChange(option.value)}
            className={cn(
              "relative px-3 py-1.5 text-sm font-medium transition-all duration-200",
              orientation === "horizontal" ? "first:rounded-l-md last:rounded-r-md" : "first:rounded-t-md last:rounded-b-md",
              disabled && "opacity-50 cursor-not-allowed"
            )}
          >
            {isActive && (
              <motion.div
                layoutId="toggleGroupIndicator"
                className="absolute inset-0 bg-white dark:bg-gray-700 rounded-md shadow-sm"
                transition={{
                  type: "spring",
                  stiffness: 500,
                  damping: 30,
                  duration: reducedMotion ? 0.1 : undefined
                }}
              />
            )}
            <span className={cn(
              "relative z-10",
              isActive ? "text-primary-700 dark:text-primary-300" : "text-gray-500 dark:text-gray-400"
            )}>
              {option.label}
            </span>
          </button>
        );
      })}
    </div>
  );
};