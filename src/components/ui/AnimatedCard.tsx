import React from "react";
import { motion } from "framer-motion";
import { cn } from "../../lib/utils";
import { cardHoverClass, cardActiveClass } from "../../styles/animation-classes";
import { prefersReducedMotion } from "../../utils/animationUtils";

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "outline" | "ghost";
  interactive?: boolean;
  elevation?: "none" | "sm" | "md" | "lg";
  animated?: boolean;
  as?: React.ElementType;
}

/**
 * Enhanced card component with animations and hover effects
 */
export const AnimatedCard = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      children,
      className,
      variant = "default",
      interactive = false,
      elevation = "none",
      animated = true,
      as = "div",
      ...props
    },
    ref
  ) => {
    // Determine if reduced motion is preferred
    const reducedMotion = prefersReducedMotion();
    
    // Base styles for all cards
    const baseStyles = "rounded-lg overflow-hidden";
    
    // Variant styles
    const variantStyles = {
      default: "bg-card text-card-foreground",
      outline: "border border-border bg-transparent",
      ghost: "bg-transparent"
    };
    
    // Elevation styles
    const elevationStyles = {
      none: "",
      sm: "shadow-sm",
      md: "shadow",
      lg: "shadow-md"
    };
    
    // Interactive styles
    const interactiveStyles = interactive 
      ? "cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2" 
      : "";
    
    // Animation variants
    const cardVariants = {
      hover: {
        y: reducedMotion ? 0 : -4,
        scale: reducedMotion ? 1 : 1.01,
        boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        transition: { duration: 0.2 }
      },
      tap: {
        scale: reducedMotion ? 1 : 0.98,
        transition: { duration: 0.1 }
      }
    };
    
    // Determine if we should use motion component or regular component
    const Component = animated && interactive && !reducedMotion ? motion[as as keyof typeof motion] || motion.div : as;
    
    // Animation props for motion component
    const animationProps = animated && interactive && !reducedMotion
      ? {
          whileHover: "hover",
          whileTap: "tap",
          variants: cardVariants,
          initial: "initial",
          transition: { duration: 0.2 }
        }
      : {};
    
    return (
      <Component
        ref={ref}
        className={cn(
          baseStyles,
          variantStyles[variant],
          elevationStyles[elevation],
          interactiveStyles,
          interactive && animated && reducedMotion && `${cardHoverClass} ${cardActiveClass}`,
          className
        )}
        {...animationProps}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

AnimatedCard.displayName = "AnimatedCard";

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex flex-col space-y-1.5 p-6", className)}
      {...props}
    />
  )
);

CardHeader.displayName = "CardHeader";

interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}

export const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn("text-lg font-semibold leading-none tracking-tight", className)}
      {...props}
    />
  )
);

CardTitle.displayName = "CardTitle";

interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

export const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn("text-sm text-muted-foreground", className)}
      {...props}
    />
  )
);

CardDescription.displayName = "CardDescription";

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
  )
);

CardContent.displayName = "CardContent";

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex items-center p-6 pt-0", className)}
      {...props}
    />
  )
);

CardFooter.displayName = "CardFooter";