import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';
import { prefersReducedMotion } from '../../utils/animationUtils';

interface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

/**
 * AnimatedButton component with hover and click animations
 */
export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  loadingText,
  leftIcon,
  rightIcon,
  fullWidth = false,
  className = '',
  disabled,
  ...props
}) => {
  const reducedMotion = prefersReducedMotion();
  
  // Base button styles
  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none transition-colors';
  
  // Variant styles
  const variantStyles = {
    primary: 'bg-notely-accent text-white hover:bg-notely-accent-dark focus:ring-2 focus:ring-notely-accent/50',
    secondary: 'bg-notely-surface text-notely-text-primary hover:bg-notely-surface-hover focus:ring-2 focus:ring-notely-surface/50',
    outline: 'border border-notely-border bg-transparent text-notely-text-primary hover:bg-notely-surface focus:ring-2 focus:ring-notely-border/50',
    ghost: 'bg-transparent text-notely-text-primary hover:bg-notely-surface focus:ring-2 focus:ring-notely-surface/50',
    link: 'bg-transparent text-notely-accent underline-offset-4 hover:underline focus:underline'
  };
  
  // Size styles
  const sizeStyles = {
    sm: 'text-xs px-3 py-1.5',
    md: 'text-sm px-4 py-2',
    lg: 'text-base px-6 py-3'
  };
  
  // Width styles
  const widthStyles = fullWidth ? 'w-full' : '';
  
  // Disabled styles
  const disabledStyles = (disabled || isLoading) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';
  
  // Animation variants
  const buttonVariants = {
    hover: {
      scale: reducedMotion ? 1 : 1.02,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: reducedMotion ? 1 : 0.98,
      transition: { duration: 0.1 }
    }
  };
  
  // Loading spinner
  const LoadingSpinner = () => (
    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  );
  
  return (
    <motion.button
      className={cn(
        baseStyles,
        variantStyles[variant],
        sizeStyles[size],
        widthStyles,
        disabledStyles,
        className
      )}
      disabled={disabled || isLoading}
      whileHover={!(disabled || isLoading) && !reducedMotion ? "hover" : undefined}
      whileTap={!(disabled || isLoading) && !reducedMotion ? "tap" : undefined}
      variants={buttonVariants}
      {...props}
    >
      {isLoading && <LoadingSpinner />}
      {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
      {isLoading && loadingText ? loadingText : children}
      {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
    </motion.button>
  );
};