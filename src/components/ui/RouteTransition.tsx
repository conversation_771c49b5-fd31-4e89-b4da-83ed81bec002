import React, { ReactNode, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  fadeTransitionVariants, 
  slideTransitionVariants, 
  scaleTransitionVariants,
  TransitionType,
  getAnimationVariant
} from '../../utils/animationUtils';

interface RouteTransitionProps {
  /**
   * The content to render
   */
  children: ReactNode;
  
  /**
   * A unique identifier for the route, typically the path
   */
  routeKey: string;
  
  /**
   * The type of transition animation to use
   */
  transitionType?: TransitionType;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Callback when exit animation completes
   */
  onExitComplete?: () => void;
}

/**
 * A component that provides smooth transitions between routes
 */
export const RouteTransition: React.FC<RouteTransitionProps> = ({
  children,
  routeKey,
  transitionType = 'fade',
  className = '',
  onExitComplete
}) => {
  // Select the appropriate animation variant based on type
  let variants;
  
  switch (transitionType) {
    case 'slide':
      variants = slideTransitionVariants;
      break;
    case 'scale':
      variants = scaleTransitionVariants;
      break;
    case 'none':
      variants = {
        initial: {},
        animate: {},
        exit: {}
      };
      break;
    case 'fade':
    default:
      variants = fadeTransitionVariants;
  }

  const animationVariant = getAnimationVariant(variants);
  
  return (
    <AnimatePresence mode="wait" onExitComplete={onExitComplete}>
      <motion.div
        key={routeKey}
        initial={animationVariant.initial}
        animate={animationVariant.animate}
        exit={animationVariant.exit}
        className={className}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

interface RouteTransitionProviderProps {
  /**
   * The content to render
   */
  children: ReactNode;
  
  /**
   * The current route path
   */
  location: string;
  
  /**
   * The type of transition animation to use
   */
  transitionType?: TransitionType;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * A provider component that wraps the application to provide route transitions
 * This component should be placed at the root level of your routing setup
 */
export const RouteTransitionProvider: React.FC<RouteTransitionProviderProps> = ({
  children,
  location,
  transitionType = 'fade',
  className = ''
}) => {
  return (
    <RouteTransition
      routeKey={location}
      transitionType={transitionType}
      className={className}
    >
      {children}
    </RouteTransition>
  );
};

/**
 * A component that simulates route transitions for demonstration purposes
 */
interface RouteSimulatorProps {
  /**
   * Map of route paths to their corresponding components
   */
  routes: Record<string, ReactNode>;
  
  /**
   * The initial route to display
   */
  initialRoute: string;
  
  /**
   * The type of transition animation to use
   */
  transitionType?: TransitionType;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

export const RouteSimulator: React.FC<RouteSimulatorProps> = ({
  routes,
  initialRoute,
  transitionType = 'fade',
  className = ''
}) => {
  const [currentRoute, setCurrentRoute] = useState(initialRoute);
  const [isNavigating, setIsNavigating] = useState(false);
  const [nextRoute, setNextRoute] = useState<string | null>(null);
  
  // Navigation function
  const navigateTo = (route: string) => {
    if (route === currentRoute || isNavigating) return;
    
    setIsNavigating(true);
    setNextRoute(route);
  };
  
  // Handle route change after exit animation completes
  const handleExitComplete = () => {
    if (nextRoute) {
      setCurrentRoute(nextRoute);
      setNextRoute(null);
      setIsNavigating(false);
    }
  };
  
  // Expose navigation function to window for demo purposes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).navigateTo = navigateTo;
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).navigateTo;
      }
    };
  }, []);
  
  return (
    <div className={className}>
      <div className="flex gap-2 mb-4">
        {Object.keys(routes).map((route) => (
          <button
            key={route}
            onClick={() => navigateTo(route)}
            className={`px-4 py-2 rounded ${
              currentRoute === route
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
            }`}
            disabled={isNavigating}
          >
            {route}
          </button>
        ))}
      </div>
      
      <RouteTransition
        routeKey={currentRoute}
        transitionType={transitionType}
        onExitComplete={handleExitComplete}
      >
        {routes[currentRoute]}
      </RouteTransition>
    </div>
  );
};