import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';

interface AnimatedNotificationProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  isVisible: boolean;
  onClose?: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  duration?: number;
  className?: string;
}

/**
 * AnimatedNotification component for displaying toast-style notifications with animations
 */
export const AnimatedNotification: React.FC<AnimatedNotificationProps> = ({
  type,
  title,
  message,
  isVisible,
  onClose,
  position = 'top-right',
  duration = 5000,
  className = ''
}) => {
  // Auto-close notification after duration
  useEffect(() => {
    if (isVisible && onClose && duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose, duration]);
  
  // Type-specific styles
  const typeStyles = {
    success: {
      bg: 'bg-green-50 dark:bg-green-900/20',
      border: 'border-green-500',
      icon: (
        <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    error: {
      bg: 'bg-red-50 dark:bg-red-900/20',
      border: 'border-red-500',
      icon: (
        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    warning: {
      bg: 'bg-yellow-50 dark:bg-yellow-900/20',
      border: 'border-yellow-500',
      icon: (
        <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
      )
    },
    info: {
      bg: 'bg-blue-50 dark:bg-blue-900/20',
      border: 'border-blue-500',
      icon: (
        <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  };
  
  // Position styles
  const positionStyles = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-4 left-1/2 -translate-x-1/2',
    'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2'
  };
  
  // Animation variants
  const variants = {
    hidden: {
      opacity: 0,
      y: position.includes('top') ? -20 : 20,
      scale: 0.95,
      transition: { duration: 0.2 }
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.3, type: 'spring', stiffness: 300, damping: 25 }
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };
  
  // Progress bar animation
  const progressVariants = {
    full: { width: '100%' },
    empty: { width: '0%', transition: { duration: duration / 1000 } }
  };
  
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className={cn(
            'fixed z-50 max-w-sm w-full shadow-lg rounded-lg overflow-hidden',
            positionStyles[position],
            className
          )}
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={variants}
        >
          <div className={cn('p-4 border-l-4', typeStyles[type].bg, typeStyles[type].border)}>
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {typeStyles[type].icon}
              </div>
              <div className="ml-3 w-0 flex-1">
                <p className="text-sm font-medium text-notely-text-primary">{title}</p>
                <p className="mt-1 text-sm text-notely-text-secondary">{message}</p>
              </div>
              {onClose && (
                <div className="ml-4 flex-shrink-0 flex">
                  <button
                    onClick={onClose}
                    className="inline-flex text-notely-text-secondary hover:text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-notely-accent"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
            
            {/* Progress bar */}
            {duration > 0 && (
              <motion.div
                className="h-1 bg-notely-accent absolute bottom-0 left-0"
                initial="full"
                animate="empty"
                variants={progressVariants}
              />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};