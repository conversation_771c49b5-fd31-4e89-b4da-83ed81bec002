import { motion, AnimatePresence } from "framer-motion";
import { ReactNode } from "react";
import { AnimationVariants, getAnimationVariant } from "../../utils/animationUtils";

interface AnimatedContainerProps {
  children: ReactNode;
  isVisible: boolean;
  variants?: AnimationVariants;
  className?: string;
  layoutId?: string;
  role?: string;
  as?: React.ElementType;
  onAnimationComplete?: () => void;
  onExitComplete?: () => void;
}

/**
 * A container component that animates its children when they enter or exit the DOM
 * Uses Framer Motion for smooth animations and respects reduced motion preferences
 */
export const AnimatedContainer = ({
  children,
  isVisible,
  variants = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  className = "",
  layoutId,
  role,
  as = "div",
  onAnimationComplete,
  onExitComplete
}: AnimatedContainerProps) => {
  const animationVariant = getAnimationVariant(variants);
  const MotionComponent = motion[as as keyof typeof motion] || motion.div;
  
  return (
    <AnimatePresence onExitComplete={onExitComplete}>
      {isVisible && (
        <MotionComponent
          initial={animationVariant.initial}
          animate={animationVariant.animate}
          exit={animationVariant.exit}
          className={className}
          layoutId={layoutId}
          role={role}
          onAnimationComplete={onAnimationComplete}
        >
          {children}
        </MotionComponent>
      )}
    </AnimatePresence>
  );
};

interface AnimatedListProps {
  children: ReactNode;
  isVisible: boolean;
  itemVariants?: AnimationVariants;
  staggerDelay?: number;
  className?: string;
  itemClassName?: string;
  role?: string;
}

/**
 * A component that animates a list of items with staggered animations
 * Each child will animate in sequence with a delay between them
 */
export const AnimatedList = ({
  children,
  isVisible,
  itemVariants = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 10 }
  },
  staggerDelay = 0.05,
  className = "",
  itemClassName = "",
  role = "list"
}: AnimatedListProps) => {
  const animationVariant = getAnimationVariant(itemVariants);
  const childrenArray = React.Children.toArray(children);
  
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.ul
          className={className}
          role={role}
          initial="initial"
          animate="animate"
          exit="exit"
          variants={{
            animate: {
              transition: {
                staggerChildren: staggerDelay
              }
            },
            exit: {
              transition: {
                staggerChildren: staggerDelay / 2,
                staggerDirection: -1
              }
            }
          }}
        >
          {childrenArray.map((child, index) => (
            <motion.li
              key={index}
              variants={animationVariant}
              className={itemClassName}
            >
              {child}
            </motion.li>
          ))}
        </motion.ul>
      )}
    </AnimatePresence>
  );
};

interface AnimatedTransitionProps {
  children: ReactNode;
  className?: string;
  layoutId?: string;
}

/**
 * A component that smoothly animates layout changes
 * Useful for elements that change size or position
 */
export const AnimatedTransition = ({
  children,
  className = "",
  layoutId
}: AnimatedTransitionProps) => {
  return (
    <motion.div
      layout
      layoutId={layoutId}
      className={className}
      transition={{ 
        type: "spring", 
        stiffness: 300, 
        damping: 30,
        duration: prefersReducedMotion() ? 0.1 : undefined
      }}
    >
      {children}
    </motion.div>
  );
};

// Helper function to check for reduced motion preference
const prefersReducedMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};