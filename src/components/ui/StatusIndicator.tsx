import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

type StatusType = 'online' | 'offline' | 'away' | 'busy' | 'loading';

interface StatusIndicatorProps {
  status: StatusType;
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  labelClassName?: string;
}

/**
 * StatusIndicator component for displaying status with animated indicators
 */
export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  size = 'md',
  className = '',
  labelClassName = ''
}) => {
  // Status colors
  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    away: 'bg-yellow-500',
    busy: 'bg-red-500',
    loading: 'bg-blue-500'
  };
  
  // Size classes
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };
  
  // Animation variants
  const pulseVariants = {
    loading: {
      scale: [1, 1.2, 1],
      opacity: [1, 0.8, 1],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    online: {
      scale: [1, 1.1, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    default: {}
  };
  
  // Determine which animation to use
  const animationVariant = status === 'loading' ? 'loading' : status === 'online' ? 'online' : 'default';
  
  return (
    <div className={cn('flex items-center', className)}>
      <motion.div
        className={cn(
          'rounded-full',
          statusColors[status],
          sizeClasses[size]
        )}
        animate={animationVariant}
        variants={pulseVariants}
      />
      
      {label && (
        <span className={cn('ml-2 text-sm text-notely-text-secondary', labelClassName)}>
          {label}
        </span>
      )}
    </div>
  );
};

interface StatusBadgeProps {
  status: StatusType | 'success' | 'error' | 'warning' | 'info';
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * StatusBadge component for displaying status badges with animations
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  label,
  size = 'md',
  className = ''
}) => {
  // Status colors and default labels
  const statusConfig = {
    online: { bg: 'bg-green-100 dark:bg-green-900/20', text: 'text-green-800 dark:text-green-300', label: 'Online' },
    offline: { bg: 'bg-gray-100 dark:bg-gray-900/20', text: 'text-gray-800 dark:text-gray-300', label: 'Offline' },
    away: { bg: 'bg-yellow-100 dark:bg-yellow-900/20', text: 'text-yellow-800 dark:text-yellow-300', label: 'Away' },
    busy: { bg: 'bg-red-100 dark:bg-red-900/20', text: 'text-red-800 dark:text-red-300', label: 'Busy' },
    loading: { bg: 'bg-blue-100 dark:bg-blue-900/20', text: 'text-blue-800 dark:text-blue-300', label: 'Loading' },
    success: { bg: 'bg-green-100 dark:bg-green-900/20', text: 'text-green-800 dark:text-green-300', label: 'Success' },
    error: { bg: 'bg-red-100 dark:bg-red-900/20', text: 'text-red-800 dark:text-red-300', label: 'Error' },
    warning: { bg: 'bg-yellow-100 dark:bg-yellow-900/20', text: 'text-yellow-800 dark:text-yellow-300', label: 'Warning' },
    info: { bg: 'bg-blue-100 dark:bg-blue-900/20', text: 'text-blue-800 dark:text-blue-300', label: 'Info' }
  };
  
  // Size classes
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-0.5',
    lg: 'text-base px-3 py-1'
  };
  
  // Animation variants
  const pulseVariants = {
    loading: {
      scale: [1, 1.05, 1],
      opacity: [1, 0.9, 1],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    default: {}
  };
  
  // Determine which animation to use
  const animationVariant = status === 'loading' ? 'loading' : 'default';
  
  return (
    <motion.span
      className={cn(
        'inline-flex items-center rounded-full font-medium',
        statusConfig[status].bg,
        statusConfig[status].text,
        sizeClasses[size],
        className
      )}
      animate={animationVariant}
      variants={pulseVariants}
    >
      <span className="flex-shrink-0">
        {status === 'loading' && (
          <svg className="animate-spin -ml-1 mr-1.5 h-2 w-2" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        )}
        {label || statusConfig[status].label}
      </span>
    </motion.span>
  );
};