import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';
import { fadeTransitionVariants, slideTransitionVariants } from '../../utils/animationUtils';

interface Tab {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
}

interface AnimatedTabsProps {
  tabs: Tab[];
  defaultTabId?: string;
  variant?: 'underline' | 'pills' | 'boxed';
  className?: string;
  tabsClassName?: string;
  contentClassName?: string;
  onChange?: (tabId: string) => void;
}

/**
 * AnimatedTabs component with smooth transitions between tab content
 */
export const AnimatedTabs: React.FC<AnimatedTabsProps> = ({
  tabs,
  defaultTabId,
  variant = 'underline',
  className = '',
  tabsClassName = '',
  contentClassName = '',
  onChange
}) => {
  const [activeTabId, setActiveTabId] = useState<string>(defaultTabId || (tabs.length > 0 ? tabs[0].id : ''));
  
  const handleTabChange = (tabId: string) => {
    if (tabId !== activeTabId) {
      setActiveTabId(tabId);
      if (onChange) {
        onChange(tabId);
      }
    }
  };
  
  // Get the active tab content
  const activeTab = tabs.find(tab => tab.id === activeTabId);
  
  // Determine tab styles based on variant
  const getTabStyles = (isActive: boolean, isDisabled: boolean) => {
    const baseStyles = 'px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none';
    const disabledStyles = 'opacity-50 cursor-not-allowed';
    
    if (isDisabled) {
      return cn(baseStyles, disabledStyles);
    }
    
    switch (variant) {
      case 'underline':
        return cn(
          baseStyles,
          'border-b-2',
          isActive 
            ? 'border-notely-accent text-notely-text-primary' 
            : 'border-transparent text-notely-text-secondary hover:text-notely-text-primary hover:border-notely-border'
        );
      case 'pills':
        return cn(
          baseStyles,
          'rounded-full',
          isActive 
            ? 'bg-notely-accent text-white' 
            : 'text-notely-text-secondary hover:text-notely-text-primary hover:bg-notely-surface'
        );
      case 'boxed':
        return cn(
          baseStyles,
          'border-b',
          isActive 
            ? 'bg-notely-card border-notely-border border-b-transparent rounded-t-lg' 
            : 'border-transparent text-notely-text-secondary hover:text-notely-text-primary'
        );
      default:
        return baseStyles;
    }
  };
  
  return (
    <div className={cn('w-full', className)}>
      {/* Tab navigation */}
      <div 
        className={cn(
          'flex mb-4',
          variant === 'underline' ? 'border-b border-notely-border' : '',
          variant === 'boxed' ? 'border-b border-notely-border' : '',
          tabsClassName
        )}
      >
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={getTabStyles(tab.id === activeTabId, !!tab.disabled)}
            onClick={() => !tab.disabled && handleTabChange(tab.id)}
            disabled={tab.disabled}
            aria-selected={tab.id === activeTabId}
            role="tab"
          >
            {tab.label}
            {variant === 'underline' && tab.id === activeTabId && (
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-notely-accent"
                layoutId="underline"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </button>
        ))}
      </div>
      
      {/* Tab content with animation */}
      <div className={cn('relative', contentClassName)}>
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTabId}
            initial={slideTransitionVariants.initial}
            animate={slideTransitionVariants.animate}
            exit={slideTransitionVariants.exit}
            transition={{ duration: 0.3 }}
            className="w-full"
          >
            {activeTab?.content}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AnimatedTabs;