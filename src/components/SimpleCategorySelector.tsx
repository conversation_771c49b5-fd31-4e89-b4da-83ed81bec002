import React, { useRef, useEffect, useState } from 'react';
import { formatForDisplay } from '../utils/formatUtils';

interface SimpleCategorySelectorProps {
  categories: string[];
  selectedCategory: string | null;
  onCategorySelect: (category: string | null) => void;
  className?: string;
  categoryCounts?: Record<string, number>;
}

const SimpleCategorySelector: React.FC<SimpleCategorySelectorProps> = ({
  categories,
  selectedCategory,
  onCategorySelect,
  className = '',
  categoryCounts = {}
}) => {
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkScroll = () => {
      if (containerRef.current) {
        const { scrollWidth, clientWidth } = containerRef.current;
        setShowScrollButtons(scrollWidth > clientWidth);
      }
    };

    checkScroll();
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, [categories]);

  const scroll = (direction: 'left' | 'right') => {
    if (containerRef.current) {
      const scrollAmount = direction === 'left' ? -200 : 200;
      containerRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  if (categories.length === 0) {
    return null;
  }

  const getButtonStyles = (isSelected: boolean) => {
    const baseStyles = 'text-[11px] font-medium rounded-lg px-2.5 py-1 transition-all duration-200 focus:outline-none relative hover:scale-105';

    if (isSelected) {
      return `${baseStyles} bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg hover:shadow-purple-500/25 hover:from-purple-600 hover:to-indigo-600 pr-5 after:content-['✓'] after:absolute after:top-1/2 after:right-1.5 after:text-[10px] after:text-white/80 after:-translate-y-1/2`;
    }

    return `${baseStyles} bg-notely-surface text-notely-text-secondary border border-notely-border/10 dark:border-notely-border-dark/20 hover:bg-notely-card hover:text-notely-text-primary hover:shadow-lg hover:shadow-purple-500/10`;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Scroll Left Button */}
      {showScrollButtons && (
        <button
          onClick={() => scroll('left')}
          className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-gradient-to-r from-black/10 to-transparent px-2 py-4 text-white/80 hover:from-black/20 transition-all duration-200 ease-out hover:scale-110 active:scale-95"
          aria-label="Scroll left"
        >
          ◀
        </button>
      )}
      
      {/* Categories Container */}
      <div
        ref={containerRef}
        className="flex gap-2 py-1.5 overflow-x-auto scrollbar-hide scroll-smooth"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => onCategorySelect(selectedCategory === category ? null : category)}
            className={getButtonStyles(selectedCategory === category)}
          >
            {formatForDisplay(category)}
            {categoryCounts[category] > 0 && (
              <span className="ml-1.5 text-[10px] font-medium rounded-md bg-notely-surface/80 dark:bg-notely-dark-hover/80 text-notely-text-muted dark:text-notely-text-secondary border border-notely-border/5 dark:border-notely-border-dark/10 px-1.5">
                {categoryCounts[category]}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Scroll Right Button */}
      {showScrollButtons && (
        <button
          onClick={() => scroll('right')}
          className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-gradient-to-l from-black/10 to-transparent px-2 py-4 text-white/80 hover:from-black/20 transition-all duration-200 ease-out hover:scale-110 active:scale-95"
          aria-label="Scroll right"
        >
          ▶
        </button>
      )}
    </div>
  );
};

export default SimpleCategorySelector;
