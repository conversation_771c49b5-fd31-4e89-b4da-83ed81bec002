import React, { useState } from 'react';
import { PageTransition, ViewTransition, TransitionType } from './ui/PageTransition';
import { AnimatedButton } from './ui/AnimatedButton';

// Sample page content components
const Page1 = () => (
  <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">First Page</h2>
    <p className="mb-4">This is the content of the first page. It animates smoothly when switching between pages.</p>
    <p>The transition is handled by the PageTransition component.</p>
  </div>
);

const Page2 = () => (
  <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">Second Page</h2>
    <p className="mb-4">Welcome to the second page! Notice how the content transitions smoothly.</p>
    <p>Different transition types can be applied to create various effects.</p>
  </div>
);

const Page3 = () => (
  <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
    <h2 className="text-xl font-bold mb-4">Third Page</h2>
    <p className="mb-4">This is the third page in our demo.</p>
    <p>Page transitions improve the user experience by making navigation feel more natural and less abrupt.</p>
  </div>
);

/**
 * Demo component for showcasing page transitions
 */
const PageTransitionDemo: React.FC = () => {
  // State for current page and transition type
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [transitionType, setTransitionType] = useState<TransitionType>('fade');
  
  // Views for the ViewTransition component
  const views = {
    'view1': <Page1 />,
    'view2': <Page2 />,
    'view3': <Page3 />
  };
  
  const [activeView, setActiveView] = useState<string>('view1');
  
  // Handle page change
  const changePage = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };
  
  // Handle view change
  const changeView = (viewName: string) => {
    setActiveView(viewName);
  };
  
  // Handle transition type change
  const changeTransitionType = (type: TransitionType) => {
    setTransitionType(type);
  };
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Page Transition Demo</h1>
      
      {/* Transition type selector */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Transition Type</h2>
        <div className="flex flex-wrap gap-2">
          <AnimatedButton 
            variant={transitionType === 'fade' ? 'default' : 'outline'}
            onClick={() => changeTransitionType('fade')}
          >
            Fade
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'slide' ? 'default' : 'outline'}
            onClick={() => changeTransitionType('slide')}
          >
            Slide
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'scale' ? 'default' : 'outline'}
            onClick={() => changeTransitionType('scale')}
          >
            Scale
          </AnimatedButton>
          <AnimatedButton 
            variant={transitionType === 'none' ? 'default' : 'outline'}
            onClick={() => changeTransitionType('none')}
          >
            None
          </AnimatedButton>
        </div>
      </div>
      
      {/* PageTransition Demo */}
      <section className="mb-8">
        <h2 className="text-lg font-semibold mb-4">PageTransition Component</h2>
        
        {/* Navigation buttons */}
        <div className="flex flex-wrap gap-2 mb-6">
          <AnimatedButton 
            variant={currentPage === 1 ? 'default' : 'outline'}
            onClick={() => changePage(1)}
          >
            Page 1
          </AnimatedButton>
          <AnimatedButton 
            variant={currentPage === 2 ? 'default' : 'outline'}
            onClick={() => changePage(2)}
          >
            Page 2
          </AnimatedButton>
          <AnimatedButton 
            variant={currentPage === 3 ? 'default' : 'outline'}
            onClick={() => changePage(3)}
          >
            Page 3
          </AnimatedButton>
        </div>
        
        {/* Page content with transition */}
        <div className="min-h-[200px]">
          <PageTransition
            key={currentPage}
            type={transitionType}
          >
            {currentPage === 1 && <Page1 />}
            {currentPage === 2 && <Page2 />}
            {currentPage === 3 && <Page3 />}
          </PageTransition>
        </div>
      </section>
      
      {/* ViewTransition Demo */}
      <section className="mb-8">
        <h2 className="text-lg font-semibold mb-4">ViewTransition Component</h2>
        
        {/* Navigation buttons */}
        <div className="flex flex-wrap gap-2 mb-6">
          <AnimatedButton 
            variant={activeView === 'view1' ? 'default' : 'outline'}
            onClick={() => changeView('view1')}
          >
            View 1
          </AnimatedButton>
          <AnimatedButton 
            variant={activeView === 'view2' ? 'default' : 'outline'}
            onClick={() => changeView('view2')}
          >
            View 2
          </AnimatedButton>
          <AnimatedButton 
            variant={activeView === 'view3' ? 'default' : 'outline'}
            onClick={() => changeView('view3')}
          >
            View 3
          </AnimatedButton>
        </div>
        
        {/* View content with transition */}
        <div className="min-h-[200px]">
          <ViewTransition
            activeView={activeView}
            views={views}
            transitionType={transitionType}
          />
        </div>
      </section>
      
      {/* Usage examples */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Implementation Examples</h2>
        <div className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg">
          <pre className="text-sm overflow-x-auto">
            {`
// Basic usage
<PageTransition>
  <YourPageContent />
</PageTransition>

// With specific transition type
<PageTransition type="slide">
  <YourPageContent />
</PageTransition>

// With a key for proper transitions
<PageTransition key={pageId} type="fade">
  <YourPageContent />
</PageTransition>

// ViewTransition component for switching between views
<ViewTransition
  activeView={currentView}
  views={{
    'home': <HomePage />,
    'about': <AboutPage />,
    'contact': <ContactPage />
  }}
  transitionType="scale"
/>
            `}
          </pre>
        </div>
      </section>
    </div>
  );
};

export default PageTransitionDemo;