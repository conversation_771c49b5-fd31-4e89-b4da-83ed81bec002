import React, { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface CategoryTagToggleProps {
  categories: string[];
  tags: string[];
  allCategories: string[];
  allTags: string[];
  onCategoriesChange: (categories: string[]) => void;
  onTagsChange: (tags: string[]) => void;
  categoriesMessage?: { text: string; type: 'success' | 'error' } | null;
  tagsMessage?: { text: string; type: 'success' | 'error' } | null;
  maxCategories?: number;
  maxTags?: number;
  canEdit?: boolean;
}

const CategoryTagToggle: React.FC<CategoryTagToggleProps> = ({
  categories,
  tags,
  allCategories,
  allTags,
  onCategoriesChange,
  onTagsChange,
  categoriesMessage,
  tagsMessage,
  maxCategories = 3,
  maxTags = 5,
  canEdit = true,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'categories' | 'tags'>('categories');
  const [showAddForm, setShowAddForm] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);

  const currentItems = activeTab === 'categories' ? categories : tags;
  const allItems = activeTab === 'categories' ? allCategories : allTags;
  const maxItems = activeTab === 'categories' ? maxCategories : maxTags;
  const currentMessage = activeTab === 'categories' ? categoriesMessage : tagsMessage;
  const onChange = activeTab === 'categories' ? onCategoriesChange : onTagsChange;

  // Filter suggestions based on input
  useEffect(() => {
    if (inputValue.trim() === '') {
      setSuggestions([]);
      return;
    }
    const lowerInput = inputValue.toLowerCase();
    const filtered = allItems
      .filter(item =>
        item.toLowerCase().includes(lowerInput) &&
        !currentItems.some(sel => sel.toLowerCase() === item.toLowerCase())
      )
      .slice(0, 5);
    setSuggestions(filtered);
  }, [inputValue, allItems, currentItems]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowAddForm(false);
        setInputValue('');
        setSuggestions([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleAddItem = (itemToAdd: string) => {
    const trimmedItem = itemToAdd.trim();
    if (
      trimmedItem &&
      currentItems.length < maxItems &&
      !currentItems.some(item => item.toLowerCase() === trimmedItem.toLowerCase())
    ) {
      const newItems = [...currentItems, trimmedItem];
      onChange(newItems);
      setInputValue('');
      setSuggestions([]);
      setShowAddForm(false);
    }
  };

  const handleRemoveItem = (itemToRemove: string) => {
    const newItems = currentItems.filter(item => item !== itemToRemove);
    onChange(newItems);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      if (inputValue.trim()) {
        handleAddItem(inputValue);
      }
    }
    if (e.key === 'Escape') {
      setShowAddForm(false);
      setInputValue('');
      setSuggestions([]);
    }
  };

  const handleAddClick = () => {
    setShowAddForm(true);
    setTimeout(() => inputRef.current?.focus(), 100);
  };

  const getRemainingCount = () => {
    return maxItems - currentItems.length;
  };

  return (
    <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-2.5 w-full">
      {/* Tabs */}
      <div className="inline-flex bg-zinc-800 p-1 rounded-md mb-2.5">
        <button
          onClick={() => setActiveTab('categories')}
          className={`px-3 py-1 text-xs font-medium rounded-md transition-colors duration-200 ${
            activeTab === 'categories'
              ? 'bg-zinc-700 text-white shadow-inner'
              : 'text-zinc-400 hover:text-white'
          }`}
        >
          {t('postViewer.categories')}
        </button>
        <button
          onClick={() => setActiveTab('tags')}
          className={`px-3 py-1 text-xs font-medium rounded-md transition-colors duration-200 ${
            activeTab === 'tags'
              ? 'bg-zinc-700 text-white shadow-inner'
              : 'text-zinc-400 hover:text-white'
          }`}
        >
          {t('postViewer.tags')}
        </button>
      </div>

      {/* Message Display */}
      {currentMessage && (
        <div className={`mb-2 p-1.5 text-[10px] rounded-md ${
          currentMessage.type === 'success'
            ? 'bg-green-900/20 text-green-300 border border-green-800/30'
            : 'bg-red-900/20 text-red-300 border border-red-800/30'
        }`}>
          {currentMessage.text}
        </div>
      )}

      {/* Tab Content */}
      <div className="transition-opacity duration-200">
        {/* Pills Container */}
        <div className="flex flex-wrap gap-1.5 items-center min-h-[20px] max-h-24 overflow-y-auto">
          {currentItems.map(item => (
            <span
              key={item}
              className="bg-zinc-700 hover:bg-zinc-600 text-xs px-2.5 py-[3px] rounded-full flex items-center gap-1 shadow-inner shadow-zinc-800 transition-colors duration-150"
            >
              {item}
              {canEdit && (
                <button
                  onClick={() => handleRemoveItem(item)}
                  className="text-zinc-400 hover:text-red-400 text-xs leading-none transition-colors duration-200 focus:outline-none"
                  aria-label={`Remove ${item}`}
                >
                  ×
                </button>
              )}
            </span>
          ))}

          {/* Add Button or Input */}
          {canEdit && currentItems.length < maxItems && (
            <>
              {!showAddForm ? (
                <button
                  onClick={handleAddClick}
                  className="ml-1 text-blue-400 text-xs hover:underline transition-colors duration-200 font-medium"
                >
                  + Add
                </button>
              ) : (
                <div className="relative ml-1">
                  <input
                    ref={inputRef}
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={`Add ${activeTab === 'categories' ? 'category' : 'tag'}...`}
                    className="px-2.5 py-[3px] text-xs rounded-full bg-zinc-800 border border-zinc-600 text-white placeholder-zinc-400 focus:outline-none focus:ring-1 focus:ring-blue-400 min-w-[100px]"
                  />
                  
                  {/* Suggestions Dropdown */}
                  {(suggestions.length > 0 || inputValue.trim()) && (
                    <ul
                      ref={dropdownRef}
                      className="absolute top-full left-0 right-0 mt-1 border border-zinc-600 rounded-md bg-zinc-800 shadow-lg max-h-24 overflow-y-auto z-10"
                    >
                      {suggestions.map(suggestion => (
                        <li
                          key={suggestion}
                          onClick={() => handleAddItem(suggestion)}
                          className="px-2.5 py-1 text-xs text-white hover:bg-zinc-700 cursor-pointer transition-colors duration-150"
                        >
                          {suggestion}
                        </li>
                      ))}
                      {/* Add new option */}
                      {inputValue.trim() &&
                       !suggestions.some(s => s.toLowerCase() === inputValue.trim().toLowerCase()) &&
                       !currentItems.some(i => i.toLowerCase() === inputValue.trim().toLowerCase()) && (
                        <li
                          onClick={() => handleAddItem(inputValue)}
                          className="px-2.5 py-1 text-xs text-blue-400 hover:bg-zinc-700 cursor-pointer border-t border-zinc-600 transition-colors duration-150"
                        >
                          Add "{inputValue.trim()}"
                        </li>
                      )}
                    </ul>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        {/* Remaining Info */}
        {canEdit && (
          <p className="text-[10px] text-zinc-500 mt-1">
            {getRemainingCount()} remaining
          </p>
        )}

        {/* Empty State */}
        {currentItems.length === 0 && (
          <p className="text-[10px] text-zinc-500 italic mt-1">
            {activeTab === 'categories' ? t('postViewer.noCategories') : t('postViewer.noTags')}
          </p>
        )}
      </div>
    </div>
  );
};

export default CategoryTagToggle;
