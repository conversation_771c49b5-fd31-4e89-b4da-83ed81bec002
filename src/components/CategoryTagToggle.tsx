import React, { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface CategoryTagToggleProps {
  categories: string[];
  tags: string[];
  allCategories: string[];
  allTags: string[];
  onCategoriesChange: (categories: string[]) => void;
  onTagsChange: (tags: string[]) => void;
  categoriesMessage?: { text: string; type: 'success' | 'error' } | null;
  tagsMessage?: { text: string; type: 'success' | 'error' } | null;
  maxCategories?: number;
  maxTags?: number;
  canEdit?: boolean;
}

const CategoryTagToggle: React.FC<CategoryTagToggleProps> = ({
  categories,
  tags,
  allCategories,
  allTags,
  onCategoriesChange,
  onTagsChange,
  categoriesMessage,
  tagsMessage,
  maxCategories = 3,
  maxTags = 5,
  canEdit = true,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'categories' | 'tags'>('categories');
  const [showAddForm, setShowAddForm] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);

  const currentItems = activeTab === 'categories' ? categories : tags;
  const allItems = activeTab === 'categories' ? allCategories : allTags;
  const maxItems = activeTab === 'categories' ? maxCategories : maxTags;
  const currentMessage = activeTab === 'categories' ? categoriesMessage : tagsMessage;
  const onChange = activeTab === 'categories' ? onCategoriesChange : onTagsChange;

  // Filter suggestions based on input
  useEffect(() => {
    if (inputValue.trim() === '') {
      setSuggestions([]);
      return;
    }
    const lowerInput = inputValue.toLowerCase();
    const filtered = allItems
      .filter(item =>
        item.toLowerCase().includes(lowerInput) &&
        !currentItems.some(sel => sel.toLowerCase() === item.toLowerCase())
      )
      .slice(0, 5);
    setSuggestions(filtered);
  }, [inputValue, allItems, currentItems]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowAddForm(false);
        setInputValue('');
        setSuggestions([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleAddItem = (itemToAdd: string) => {
    const trimmedItem = itemToAdd.trim();
    if (
      trimmedItem &&
      currentItems.length < maxItems &&
      !currentItems.some(item => item.toLowerCase() === trimmedItem.toLowerCase())
    ) {
      const newItems = [...currentItems, trimmedItem];
      onChange(newItems);
      setInputValue('');
      setSuggestions([]);
      setShowAddForm(false);
    }
  };

  const handleRemoveItem = (itemToRemove: string) => {
    const newItems = currentItems.filter(item => item !== itemToRemove);
    onChange(newItems);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      if (inputValue.trim()) {
        handleAddItem(inputValue);
      }
    }
    if (e.key === 'Escape') {
      setShowAddForm(false);
      setInputValue('');
      setSuggestions([]);
    }
  };

  const handleAddClick = () => {
    setShowAddForm(true);
    setTimeout(() => inputRef.current?.focus(), 100);
  };

  const getRemainingCount = () => {
    return maxItems - currentItems.length;
  };

  return (
    <div className="bg-notely-card border border-notely-border/10 dark:border-notely-border-dark/20 rounded-xl p-4 w-full">
      {/* Tabs */}
      <div className="flex gap-4 border-b border-notely-border/20 dark:border-notely-border-dark/30 mb-3">
        <button
          onClick={() => setActiveTab('categories')}
          className={`pb-1 text-sm font-medium transition-all duration-200 ${
            activeTab === 'categories'
              ? 'border-b-2 border-white dark:border-notely-text-primary text-notely-text-primary'
              : 'text-notely-text-muted hover:text-notely-text-secondary'
          }`}
        >
          {t('postViewer.categories')}
        </button>
        <button
          onClick={() => setActiveTab('tags')}
          className={`pb-1 text-sm font-medium transition-all duration-200 ${
            activeTab === 'tags'
              ? 'border-b-2 border-white dark:border-notely-text-primary text-notely-text-primary'
              : 'text-notely-text-muted hover:text-notely-text-secondary'
          }`}
        >
          {t('postViewer.tags')}
        </button>
      </div>

      {/* Message Display */}
      {currentMessage && (
        <div className={`mb-3 p-2 text-xs rounded-lg ${
          currentMessage.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200/50 dark:border-green-800/30' 
            : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200/50 dark:border-red-800/30'
        }`}>
          {currentMessage.text}
        </div>
      )}

      {/* Tab Content */}
      <div>
        {/* Pills Container */}
        <div className="flex flex-wrap gap-2 items-center min-h-[32px]">
          {currentItems.map(item => (
            <span
              key={item}
              className="bg-notely-surface dark:bg-notely-dark-hover text-sm px-3 py-1 rounded-full flex items-center gap-1.5 border border-notely-border/10 dark:border-notely-border-dark/20 shadow-sm"
            >
              {item}
              {canEdit && (
                <button
                  onClick={() => handleRemoveItem(item)}
                  className="text-notely-text-muted hover:text-red-400 dark:hover:text-red-400 text-sm transition-colors duration-200 focus:outline-none ml-1"
                  aria-label={`Remove ${item}`}
                >
                  ×
                </button>
              )}
            </span>
          ))}

          {/* Add Button or Input */}
          {canEdit && currentItems.length < maxItems && (
            <>
              {!showAddForm ? (
                <button
                  onClick={handleAddClick}
                  className="text-blue-400 dark:text-blue-300 text-sm hover:underline ml-2 transition-colors duration-200 font-medium"
                >
                  + Add
                </button>
              ) : (
                <div className="relative ml-2">
                  <input
                    ref={inputRef}
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={`Add ${activeTab === 'categories' ? 'category' : 'tag'}...`}
                    className="px-3 py-1 text-sm rounded-full bg-notely-surface border border-notely-border/20 dark:border-notely-border-dark/30 text-notely-text-primary placeholder-notely-text-muted focus:outline-none focus:ring-2 focus:ring-notely-accent/30 min-w-[120px]"
                  />
                  
                  {/* Suggestions Dropdown */}
                  {(suggestions.length > 0 || inputValue.trim()) && (
                    <ul
                      ref={dropdownRef}
                      className="absolute top-full left-0 right-0 mt-1 border border-notely-border/10 dark:border-notely-border-dark/20 rounded-lg bg-notely-card shadow-notely-md max-h-40 overflow-y-auto z-10"
                    >
                      {suggestions.map(suggestion => (
                        <li
                          key={suggestion}
                          onClick={() => handleAddItem(suggestion)}
                          className="px-3 py-2 text-sm text-notely-text-primary hover:bg-notely-surface dark:hover:bg-notely-dark-hover cursor-pointer"
                        >
                          {suggestion}
                        </li>
                      ))}
                      {/* Add new option */}
                      {inputValue.trim() &&
                       !suggestions.some(s => s.toLowerCase() === inputValue.trim().toLowerCase()) &&
                       !currentItems.some(i => i.toLowerCase() === inputValue.trim().toLowerCase()) && (
                        <li
                          onClick={() => handleAddItem(inputValue)}
                          className="px-3 py-2 text-sm text-blue-400 dark:text-blue-300 hover:bg-notely-surface dark:hover:bg-notely-dark-hover cursor-pointer border-t border-notely-border/10 dark:border-notely-border-dark/20"
                        >
                          Add "{inputValue.trim()}"
                        </li>
                      )}
                    </ul>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        {/* Remaining Info */}
        {canEdit && (
          <p className="text-xs text-notely-text-muted mt-2">
            {getRemainingCount()} remaining
          </p>
        )}

        {/* Empty State */}
        {currentItems.length === 0 && (
          <p className="text-xs text-notely-text-muted italic mt-2">
            {activeTab === 'categories' ? t('postViewer.noCategories') : t('postViewer.noTags')}
          </p>
        )}
      </div>
    </div>
  );
};

export default CategoryTagToggle;
