/* Animation keyframes - with stronger, more visible effects */
@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  0% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-10px); }
}

@keyframes slideInRight {
  0% { transform: translateX(30px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutLeft {
  0% { transform: translateX(0); opacity: 1; }
  100% { transform: translateX(-30px); opacity: 0; }
}

@keyframes scaleIn {
  0% { transform: scale(0.9); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes scaleOut {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(0.9); opacity: 0; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Animation utility classes with !important to ensure they're applied */
.animate-fadeIn {
  animation: fadeIn 0.3s ease forwards !important;
}

.animate-fadeOut {
  animation: fadeOut 0.3s ease forwards !important;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease forwards !important;
}

.animate-slideOutLeft {
  animation: slideOutLeft 0.3s ease forwards !important;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease forwards !important;
}

.animate-scaleOut {
  animation: scaleOut 0.3s ease forwards !important;
}

.animate-spin {
  animation: spin 1s linear infinite !important;
}

/* Page transition wrapper styles */
.page-transition-wrapper {
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* Button hover animations */
.hover-scale {
  transition: transform 0.2s ease !important;
}

.hover-scale:hover {
  transform: scale(1.05) !important;
}

.hover-scale:active {
  transform: scale(0.98) !important;
}

/* Stagger animations for lists */
.stagger-item {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeIn 0.5s ease forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

/* Loading spinner animation */
.loading-spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Progress bar animation */
@keyframes progressBar {
  0% { width: 0%; }
  100% { width: 100%; }
}

.animate-progress {
  animation: progressBar 1.5s linear infinite;
}

/* Notification animations */
@keyframes slideInDown {
  0% { transform: translateY(-20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

.notification-enter {
  animation: slideInDown 0.3s ease forwards;
}

.animate-slideInDown {
  animation: slideInDown 0.3s ease forwards !important;
}

/* Card hover effects */
.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button animations */
.btn-primary {
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
}