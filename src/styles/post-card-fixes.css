/* Post card user info alignment fixes */

/* Fix vertical alignment between avatar and text */
.flex-shrink-0 + .flex-grow.min-w-0 {
  display: flex;
  align-items: flex-start;
}

/* Remove space-y gap and replace with tighter spacing */
.flex-col.space-y-0 {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin: 0;
  padding: 0;
}

/* Adjust line heights for better text block appearance */
.author-name {
  line-height: 1.1 !important;
  margin-bottom: 1px !important;
  padding-bottom: 0 !important;
}

.author-handle {
  line-height: 1.1 !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
}
