/**
 * Animation utility classes and settings
 * This file provides animation configuration and utility functions
 */

// Default animation settings
export const DEFAULT_ANIMATION_SETTINGS = {
  enabled: true,
  transitionType: 'fade' as 'fade' | 'slide' | 'scale' | 'none',
  duration: 300,
  reducedMotion: false
};

// Card animation classes
export const cardHoverClass = "transition-transform hover:-translate-y-1 hover:shadow-md";
export const cardActiveClass = "active:scale-98";

// Animation class mapping for different transition types
export const ANIMATION_CLASSES = {
  fade: {
    enter: 'animate-fadeIn',
    exit: 'animate-fadeOut'
  },
  slide: {
    enter: 'animate-slideIn',
    exit: 'animate-slideOut'
  },
  scale: {
    enter: 'animate-scaleIn',
    exit: 'animate-scaleOut'
  },
  none: {
    enter: '',
    exit: ''
  }
};

// Function to get animation settings from storage
export const getAnimationSettings = async () => {
  try {
    const result = await chrome.storage.local.get('animationSettings');
    return result.animationSettings || DEFAULT_ANIMATION_SETTINGS;
  } catch (error) {
    console.error('Error getting animation settings:', error);
    return DEFAULT_ANIMATION_SETTINGS;
  }
};

// Function to save animation settings to storage
export const saveAnimationSettings = async (settings: typeof DEFAULT_ANIMATION_SETTINGS) => {
  try {
    await chrome.storage.local.set({ animationSettings: settings });
    return true;
  } catch (error) {
    console.error('Error saving animation settings:', error);
    return false;
  }
};

// Function to check if reduced motion is preferred
export const prefersReducedMotion = (): boolean => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Function to get appropriate animation type based on user preference
export const getEffectiveTransitionType = async (): Promise<'fade' | 'slide' | 'scale' | 'none'> => {
  const settings = await getAnimationSettings();
  
  // If animations are disabled or reduced motion is preferred, return 'none'
  if (!settings.enabled || (settings.reducedMotion && prefersReducedMotion())) {
    return 'none';
  }
  
  return settings.transitionType;
};